import logging
import os

from fastapi import (
    Header,
    HTTPException,
)

from server.core import manager

logger = logging.getLogger(__name__)


# --- API Key 认证 ---
SERVER_OWN_API_KEY = os.environ.get("API_KEY")


async def verify_api_key(authorization: str | None = Header(None)):
    # 优先使用 Userscript 设置的 API Key
    expected_api_key = manager.userscript_api_key or SERVER_OWN_API_KEY

    if not expected_api_key:
        logger.warning(
            "API Key 验证跳过 - Userscript 未设置 API Key 且服务器环境变量 API_KEY 未设置。API 处于无保护状态！"
        )
        return  # 无 Key 可验证，则放行

    if not authorization:
        logger.warning("请求缺少 Authorization header")
        raise HTTPException(status_code=401, detail="Missing Authorization header")

    try:
        scheme, _, token = authorization.partition(" ")  # 使用 partition 更安全
        if scheme.lower() != "bearer" or not token:
            raise HTTPException(status_code=401, detail="无效的认证方案或 Token 为空。请使用 Bearer token。")

        if token != expected_api_key:
            # 为安全起见，不记录完整的 token
            logger.warning(
                f"无效的 API Key 尝试。收到 Token (前5位): {token[:5]}..., 期望 Key (前5位): {expected_api_key[:5]}..."
            )
            raise HTTPException(status_code=401, detail="无效的 API Key。")
    except ValueError:  # partition 不会抛 ValueError，但保留以防其他 split 方式
        raise HTTPException(status_code=401, detail="无效的 Authorization 格式。请使用 'Bearer YOUR_API_KEY'。")

    logger.debug("API Key 验证成功。")  # Debug 级别，避免过多日志
