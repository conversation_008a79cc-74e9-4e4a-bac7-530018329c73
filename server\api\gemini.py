import json
import logging
from typing import Any

from fastapi import (
    HTTP<PERSON>xception,
    WebSocket,
    WebSocketDisconnect,
)
from fastapi.websockets import WebSocketState

from server.core import manager

logger = logging.getLogger(__name__)


async def websocket_endpoint_userscript(websocket: WebSocket):
    await manager.connect_userscript(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            message: dict[str, Any] = json.loads(data)
            msg_type = message.get("type")
            logger.info(f"从 Userscript 收到消息: {msg_type}")

            request_id = message.get("request_id")  # 很多消息类型都有 request_id

            if msg_type == "userscript_ready":
                user_data: dict[str, Any] = message.get("data", {})
                manager.set_userscript_info(user_data.get("apiKey"), user_data.get("models", []))

            elif msg_type == "api_response":
                if request_id and request_id in manager.pending_api_requests:
                    future = manager.pending_api_requests.pop(request_id)
                    future.set_result(message.get("data"))
                else:
                    logger.warning(f"收到未知或已处理 request_id 的 api_response: {request_id}")

            elif msg_type == "api_stream_chunk":
                if request_id and request_id in manager.pending_api_streams:
                    queue = manager.pending_api_streams[request_id]
                    await queue.put(message.get("data"))
                else:
                    logger.warning(f"收到未知或已处理 request_id 的 api_stream_chunk: {request_id}")

            elif msg_type == "api_stream_end":
                if request_id and request_id in manager.pending_api_streams:
                    queue = manager.pending_api_streams.pop(request_id)  # 从挂起中移除
                    await queue.put(None)  # None作为结束信号
                else:
                    logger.warning(f"收到未知或已处理 request_id 的 api_stream_end: {request_id}")

            elif msg_type == "api_error":
                error_detail = message.get("error", "Userscript reported an unknown error.")
                if request_id:
                    if request_id in manager.pending_api_requests:
                        future = manager.pending_api_requests.pop(request_id)
                        future.set_exception(HTTPException(status_code=500, detail=f"Userscript Error: {error_detail}"))
                    elif request_id in manager.pending_api_streams:
                        queue = manager.pending_api_streams.pop(request_id)  # 从挂起中移除
                        await queue.put({"error": error_detail})  # 发送错误信息块
                        await queue.put(None)  # 发送结束信号
                    else:
                        logger.warning(f"收到未知或已处理 request_id 的 api_error: {request_id}")
                else:  # 没有 request_id 的全局错误
                    logger.error(f"Userscript报告了一个全局错误: {error_detail}")

    except WebSocketDisconnect:
        logger.info(f"Userscript WebSocket 连接断开 (code: {websocket.client_state}).")
        manager.disconnect_userscript(websocket)
    except json.JSONDecodeError:
        logger.error("从 Userscript 收到无法解析的 JSON 数据。")
        # 可以选择关闭连接或忽略
        if websocket.client_state == WebSocketState.CONNECTED:
            await websocket.close(code=1003, reason="Invalid JSON data received")
        manager.disconnect_userscript(websocket)
    except Exception as e:
        logger.error(f"Userscript WebSocket 异常: {e}", exc_info=True)
        if websocket.client_state == WebSocketState.CONNECTED:
            await websocket.close(code=1011, reason=f"Server error: {e}")
        manager.disconnect_userscript(websocket)
