import os
from typing import Any

from fastapi.websockets import WebSocketState

from server.core import manager

SERVER_OWN_API_KEY = os.environ.get("API_KEY")


async def root() -> dict[str, Any]:
    return {
        "status": "online",
        "message": "Gemini Userscript Bridge API Server is running.",
        "userscript_connected": manager.active_userscript is not None
        and manager.active_userscript.client_state == WebSocketState.CONNECTED,
        "userscript_api_key_set_by_userscript": manager.userscript_api_key is not None,
        "server_api_key_set_in_env": SERVER_OWN_API_KEY is not None,
        "effective_api_key_present": bool(manager.userscript_api_key or SERVER_OWN_API_KEY),
        "available_models_count": len(manager.available_models),
        "current_models": [m.get("id") for m in manager.available_models] if manager.available_models else "N/A",
    }
