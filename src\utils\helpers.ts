/**
 * AI Studio 2 API - Utility Helper Functions
 *
 * 通用工具函数集合
 */

import type { LogLevel, Logger } from '../types';
import { LOG_CONFIG, RETRY_CONFIG } from '../config/constants';

// --- 日志工具 ---
export class ConsoleLogger implements Logger {
  private level: LogLevel;

  constructor(level: LogLevel = LOG_CONFIG.DEFAULT_LEVEL) {
    this.level = level;
  }

  private shouldLog(level: LogLevel): boolean {
    const levels: LogLevel[] = ['debug', 'info', 'warn', 'error'];
    return levels.indexOf(level) >= levels.indexOf(this.level);
  }

  private formatMessage(level: LogLevel, message: string): string {
    const timestamp = new Date().toISOString();
    const prefix = LOG_CONFIG.CONSOLE_PREFIX;
    return `${timestamp} ${prefix} [${level.toUpperCase()}] ${message}`;
  }

  debug(message: string, ...args: any[]): void {
    if (this.shouldLog('debug')) {
      console.debug(this.formatMessage('debug', message), ...args);
    }
  }

  info(message: string, ...args: any[]): void {
    if (this.shouldLog('info')) {
      console.info(this.formatMessage('info', message), ...args);
    }
  }

  warn(message: string, ...args: any[]): void {
    if (this.shouldLog('warn')) {
      console.warn(this.formatMessage('warn', message), ...args);
    }
  }

  error(message: string, ...args: any[]): void {
    if (this.shouldLog('error')) {
      console.error(this.formatMessage('error', message), ...args);
    }
  }

  setLevel(level: LogLevel): void {
    this.level = level;
  }
}

// --- 全局日志实例 ---
export const logger = new ConsoleLogger();

// --- 延迟工具 ---
export const delay = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// --- 重试工具 ---
export async function retry<T>(
  fn: () => Promise<T>,
  options: {
    maxAttempts?: number;
    delay?: number;
    backoffMultiplier?: number;
    maxDelay?: number;
    onRetry?: (attempt: number, error: Error) => void;
  } = {}
): Promise<T> {
  const {
    maxAttempts = RETRY_CONFIG.MAX_ATTEMPTS,
    delay: initialDelay = RETRY_CONFIG.INITIAL_DELAY,
    backoffMultiplier = RETRY_CONFIG.BACKOFF_MULTIPLIER,
    maxDelay = RETRY_CONFIG.MAX_DELAY,
    onRetry,
  } = options;

  let lastError: Error;
  let currentDelay = initialDelay;

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));

      if (attempt === maxAttempts) {
        throw lastError;
      }

      if (onRetry) {
        onRetry(attempt, lastError);
      }

      await delay(currentDelay);
      currentDelay = Math.min(currentDelay * backoffMultiplier, maxDelay);
    }
  }

  throw lastError!;
}

// --- DOM 工具 ---
export function waitForElement(
  selector: string,
  timeout: number = 10000,
  parent: Document | Element = document
): Promise<Element> {
  return new Promise((resolve, reject) => {
    const element = parent.querySelector(selector);
    if (element) {
      resolve(element);
      return;
    }

    const observer = new MutationObserver(() => {
      const element = parent.querySelector(selector);
      if (element) {
        observer.disconnect();
        resolve(element);
      }
    });

    observer.observe(parent, {
      childList: true,
      subtree: true,
    });

    setTimeout(() => {
      observer.disconnect();
      reject(new Error(`Element not found: ${selector}`));
    }, timeout);
  });
}

export function waitForElements(
  selectors: string[],
  timeout: number = 10000,
  parent: Document | Element = document
): Promise<Element> {
  return Promise.race(
    selectors.map(selector => waitForElement(selector, timeout, parent))
  );
}

export function isElementVisible(element: Element): boolean {
  if (!element) return false;

  const style = window.getComputedStyle(element);
  const htmlElement = element as HTMLElement;
  return (
    style.display !== 'none' &&
    style.visibility !== 'hidden' &&
    style.opacity !== '0' &&
    htmlElement.offsetWidth > 0 &&
    htmlElement.offsetHeight > 0
  );
}

export function isElementInteractable(element: Element): boolean {
  if (!isElementVisible(element)) return false;

  const htmlElement = element as any;
  return !htmlElement.disabled && !htmlElement.readOnly;
}

// --- 事件工具 ---
export function simulateEvent(
  element: Element,
  eventType: string,
  options: EventInit = {}
): void {
  const event = new Event(eventType, {
    bubbles: true,
    cancelable: true,
    ...options,
  });
  element.dispatchEvent(event);
}

export function simulateInput(
  element: HTMLInputElement | HTMLTextAreaElement,
  value: string
): void {
  const nativeInputValueSetter = Object.getOwnPropertyDescriptor(
    window.HTMLInputElement.prototype,
    'value'
  )?.set;

  const nativeTextAreaValueSetter = Object.getOwnPropertyDescriptor(
    window.HTMLTextAreaElement.prototype,
    'value'
  )?.set;

  if (element instanceof HTMLInputElement && nativeInputValueSetter) {
    nativeInputValueSetter.call(element, value);
  } else if (element instanceof HTMLTextAreaElement && nativeTextAreaValueSetter) {
    nativeTextAreaValueSetter.call(element, value);
  } else {
    element.value = value;
  }

  simulateEvent(element, 'input');
  simulateEvent(element, 'change');
}

export function simulateClick(element: Element): void {
  const rect = element.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;

  const mouseEvent = new MouseEvent('click', {
    bubbles: true,
    cancelable: true,
    clientX: x,
    clientY: y,
  });

  element.dispatchEvent(mouseEvent);
}

// --- 字符串工具 ---
export function escapeHtml(text: string): string {
  const div = document.createElement('div');
  div.textContent = text;
  return div.innerHTML;
}

export function unescapeHtml(html: string): string {
  const div = document.createElement('div');
  div.innerHTML = html;
  return div.textContent || '';
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
}

export function sanitizeText(text: string): string {
  return text
    .replace(/[\r\n\t]/g, ' ')
    .replace(/\s+/g, ' ')
    .trim();
}

// --- 对象工具 ---
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') return obj;
  if (obj instanceof Date) return new Date(obj.getTime()) as unknown as T;
  if (obj instanceof Array) return obj.map(item => deepClone(item)) as unknown as T;
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  return obj;
}

export function mergeObjects<T extends Record<string, any>>(
  target: T,
  ...sources: Partial<T>[]
): T {
  const result = { ...target };

  for (const source of sources) {
    for (const key in source) {
      if (source.hasOwnProperty(key)) {
        const value = source[key];
        if (value !== undefined) {
          result[key] = value as T[Extract<keyof T, string>];
        }
      }
    }
  }

  return result;
}

// --- 验证工具 ---
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function isValidJson(str: string): boolean {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
}

export function isValidModelId(modelId: string): boolean {
  return /^models\/[a-zA-Z0-9-_.]+$/.test(modelId);
}

// --- 存储工具 ---
export function safeGetValue(key: string, defaultValue: any = null): any {
  try {
    return window.GM_getValue(key, defaultValue);
  } catch (error) {
    logger.warn(`Failed to get value for key ${key}:`, error);
    return defaultValue;
  }
}

export function safeSetValue(key: string, value: any): boolean {
  try {
    window.GM_setValue(key, value);
    return true;
  } catch (error) {
    logger.warn(`Failed to set value for key ${key}:`, error);
    return false;
  }
}

// --- 时间工具 ---
export function formatTimestamp(timestamp?: number | string | Date): string {
  const date = timestamp ? new Date(timestamp) : new Date();
  return date.toISOString().replace('T', ' ').substring(0, 19);
}

export function getElapsedTime(startTime: number): string {
  const elapsed = Date.now() - startTime;
  if (elapsed < 1000) return `${elapsed}ms`;
  if (elapsed < 60000) return `${(elapsed / 1000).toFixed(1)}s`;
  return `${(elapsed / 60000).toFixed(1)}m`;
}

// --- 错误处理工具 ---
export function createError(
  message: string,
  code?: string,
  details?: any
): Error {
  const error = new Error(message);
  if (code) (error as any).code = code;
  if (details) (error as any).details = details;
  return error;
}

export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) return error.message;
  if (typeof error === 'string') return error;
  return 'Unknown error occurred';
}

export function getErrorDetails(error: unknown): any {
  if (error instanceof Error) {
    return {
      name: error.name,
      message: error.message,
      stack: error.stack,
      ...(error as any).details,
    };
  }
  return { error: String(error) };
}
