/**
 * AI Studio 2 API - Authentication Manager
 *
 * 处理 AI Studio 的认证和会话管理
 */

import type { AuthCredentials } from '../types';
import { AI_STUDIO_URLS, STORAGE_KEYS, TIMEOUTS } from '../config/constants';
import { logger } from '../utils/helpers';
import { AuthenticationError } from '../types';

export class AuthManager {
  private credentials: AuthCredentials | null = null;
  private isRefreshing = false;
  private refreshPromise: Promise<AuthCredentials> | null = null;

  constructor() {
    this.loadStoredCredentials();
  }

  /**
   * 获取当前认证凭据
   */
  public getCredentials(): AuthCredentials | null {
    return this.credentials;
  }

  /**
   * 检查是否已认证
   */
  public isAuthenticated(): boolean {
    return this.credentials !== null && this.hasValidCookies();
  }

  /**
   * 刷新认证凭据
   */
  public async refreshCredentials(): Promise<AuthCredentials> {
    if (this.isRefreshing && this.refreshPromise) {
      logger.info('认证刷新已在进行中，等待完成...');
      return this.refreshPromise;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.performRefresh();

    try {
      const credentials = await this.refreshPromise;
      this.credentials = credentials;
      this.saveCredentials();
      logger.info('认证凭据刷新成功');
      return credentials;
    } catch (error) {
      logger.error('认证凭据刷新失败:', error);
      throw error;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * 执行认证刷新
   */
  private async performRefresh(): Promise<AuthCredentials> {
    try {
      // 获取 Cookies
      const cookies = await this.getCookies();

      // 获取会话令牌
      const sessionToken = await this.getSessionToken();

      // 获取 CSRF 令牌（如果需要）
      const csrfToken = await this.getCsrfToken();

      const credentials: AuthCredentials = {
        cookies,
        sessionToken,
        csrfToken,
      };

      return credentials;
    } catch (error) {
      throw new AuthenticationError(
        `认证刷新失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 获取 Google Cookies
   */
  private async getCookies(): Promise<Record<string, string>> {
    return new Promise((resolve, reject) => {
      if (!window.GM_cookie) {
        reject(new Error('GM_cookie API 不可用'));
        return;
      }

      window.GM_cookie.list({ domain: '.google.com' }, (cookies, error) => {
        if (error) {
          reject(new Error(`获取 Cookie 失败: ${error}`));
          return;
        }

        if (!cookies || cookies.length === 0) {
          reject(new Error('未找到 Google 域名的 cookies，请确保已登录 Google 账号'));
          return;
        }

        // 查找必要的认证 cookies
        const requiredCookies = ['__Secure-1PSID', '__Secure-1PSIDTS'];
        const cookieMap: Record<string, string> = {};

        for (const cookie of cookies) {
          if (requiredCookies.includes(cookie.name)) {
            cookieMap[cookie.name] = cookie.value;
          }
        }

        // 检查必要的 cookies
        if (!cookieMap['__Secure-1PSID']) {
          reject(new Error('未找到必要的 __Secure-1PSID cookie'));
          return;
        }

        logger.debug(`成功获取 ${Object.keys(cookieMap).length} 个认证 cookies`);
        resolve(cookieMap);
      });
    });
  }

  /**
   * 获取会话令牌
   */
  private async getSessionToken(): Promise<string | undefined> {
    try {
      const response = await this.makeRequest(AI_STUDIO_URLS.BASE);
      const html = response.responseText;

      // 尝试从页面中提取会话令牌
      const tokenMatch = html.match(/"SNlM0e"\s*:\s*"([^"]+)"/);
      if (tokenMatch && tokenMatch[1]) {
        logger.debug('成功提取会话令牌');
        return tokenMatch[1];
      }

      // 尝试从 WIZ_global_data 中提取
      const wizMatch = html.match(/WIZ_global_data\s*=\s*({.*?});/s);
      if (wizMatch && wizMatch[1]) {
        try {
          const wizData = JSON.parse(wizMatch[1]);
          const token = wizData.SNlM0e;
          if (token) {
            logger.debug('从 WIZ_global_data 成功提取会话令牌');
            return token;
          }
        } catch (e) {
          logger.warn('解析 WIZ_global_data 失败:', e);
        }
      }

      logger.warn('未能从页面提取会话令牌');
      return undefined;
    } catch (error) {
      logger.error('获取会话令牌失败:', error);
      return undefined;
    }
  }

  /**
   * 获取 CSRF 令牌
   */
  private async getCsrfToken(): Promise<string | undefined> {
    try {
      // AI Studio 可能不需要 CSRF 令牌，或者使用其他机制
      // 这里预留接口，如果需要可以实现
      return undefined;
    } catch (error) {
      logger.error('获取 CSRF 令牌失败:', error);
      return undefined;
    }
  }

  /**
   * 发起 HTTP 请求
   */
  private makeRequest(url: string, options: any = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      const requestOptions = {
        method: 'GET',
        url,
        timeout: TIMEOUTS.REQUEST_TIMEOUT,
        headers: {
          'User-Agent': navigator.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate',
          'Connection': 'keep-alive',
          'Upgrade-Insecure-Requests': '1',
        },
        ...options,
        onload: (response: any) => {
          if (response.status >= 200 && response.status < 300) {
            resolve(response);
          } else {
            reject(new Error(`HTTP ${response.status}: ${response.statusText}`));
          }
        },
        onerror: (error: any) => {
          reject(new Error(`网络请求失败: ${error.statusText || 'Unknown error'}`));
        },
        ontimeout: () => {
          reject(new Error('请求超时'));
        },
      };

      if (window.GM_xmlhttpRequest) {
        window.GM_xmlhttpRequest(requestOptions);
      } else {
        reject(new Error('GM_xmlhttpRequest API 不可用'));
      }
    });
  }

  /**
   * 检查是否有有效的 cookies
   */
  private hasValidCookies(): boolean {
    if (!this.credentials?.cookies) return false;

    const requiredCookies = ['__Secure-1PSID'];
    return requiredCookies.every(name =>
      this.credentials!.cookies[name] &&
      this.credentials!.cookies[name].length > 0
    );
  }

  /**
   * 加载存储的认证凭据
   */
  private loadStoredCredentials(): void {
    try {
      const stored = window.GM_getValue(STORAGE_KEYS.AUTH_CREDENTIALS);
      if (stored && typeof stored === 'object') {
        this.credentials = stored as AuthCredentials;
        logger.debug('已加载存储的认证凭据');
      }
    } catch (error) {
      logger.warn('加载存储的认证凭据失败:', error);
    }
  }

  /**
   * 保存认证凭据
   */
  private saveCredentials(): void {
    try {
      if (this.credentials) {
        window.GM_setValue(STORAGE_KEYS.AUTH_CREDENTIALS, this.credentials);
        logger.debug('认证凭据已保存');
      }
    } catch (error) {
      logger.warn('保存认证凭据失败:', error);
    }
  }

  /**
   * 清除认证凭据
   */
  public clearCredentials(): void {
    this.credentials = null;
    try {
      window.GM_setValue(STORAGE_KEYS.AUTH_CREDENTIALS, null);
      logger.info('认证凭据已清除');
    } catch (error) {
      logger.warn('清除认证凭据失败:', error);
    }
  }

  /**
   * 获取认证头部
   */
  public getAuthHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'User-Agent': navigator.userAgent,
      'Accept': 'application/json, text/plain, */*',
      'Accept-Language': 'en-US,en;q=0.9',
      'Content-Type': 'application/json',
      'Origin': AI_STUDIO_URLS.BASE,
      'Referer': AI_STUDIO_URLS.BASE + '/',
    };

    if (this.credentials?.cookies) {
      const cookieString = Object.entries(this.credentials.cookies)
        .map(([name, value]) => `${name}=${value}`)
        .join('; ');
      headers['Cookie'] = cookieString;
    }

    if (this.credentials?.csrfToken) {
      headers['X-CSRF-Token'] = this.credentials.csrfToken;
    }

    return headers;
  }

  /**
   * 验证认证状态
   */
  public async validateAuth(): Promise<boolean> {
    try {
      if (!this.isAuthenticated()) {
        return false;
      }

      // 尝试发起一个简单的认证请求来验证凭据是否有效
      await this.makeRequest(AI_STUDIO_URLS.BASE, {
        headers: this.getAuthHeaders(),
      });

      return true;
    } catch (error) {
      logger.warn('认证验证失败:', error);
      return false;
    }
  }
}
