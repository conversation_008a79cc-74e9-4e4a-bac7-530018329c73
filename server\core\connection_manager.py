import asyncio
import json
import logging
from typing import Any, Callable, Dict, List

from fastapi import (
    Fast<PERSON><PERSON>,
    HTTPException,
    WebSocket,
    WebSocketDisconnect,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.websockets import WebSocketState

logger = logging.getLogger(__name__)


# --- FastAPI 应用实例 ---
app = FastAPI(title="Gemini Userscript Bridge API Server")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境建议限制来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# --- 服务器状态和 WebSocket 管理 ---
class ConnectionManager:
    def __init__(self):
        # Gemini 相关（保持兼容性）
        self.active_userscript: WebSocket | None = None
        self.userscript_api_key: str | None = None
        self.available_models: list[dict[str, Any]] = []
        self.pending_api_requests: dict[str, asyncio.Future[Any]] = {}
        self.pending_api_streams: dict[
            str, asyncio.Queue[dict[str, Any] | None]
        ] = {}  # Queue item can be None to signal end

        # AI Studio 相关
        self.active_aistudio_userscripts: Dict[str, WebSocket] = {}
        self.aistudio_models: List[Dict[str, Any]] = []
        self.message_handlers: List[Callable[[Dict[str, Any]], None]] = []

    async def connect_userscript(self, websocket: WebSocket):
        await websocket.accept()
        if self.active_userscript:
            logger.warning("新的 Userscript 连接请求，但已有活动连接。旧连接将被关闭。")
            try:
                # 告知旧连接被取代
                await self.active_userscript.close(code=4001, reason="New connection replaced this one")
            except Exception as e:
                logger.info(f"关闭旧 Userscript 连接时出错: {e}")
        self.active_userscript = websocket
        logger.info("Userscript 已连接")

    def disconnect_userscript(self, websocket: WebSocket | None = None):
        # 只有当断开的是当前活动的 userscript 时才进行清理
        if websocket is None or self.active_userscript == websocket:
            logger.info("Userscript 已断开")
            self.active_userscript = None
            # 清理挂起的请求和流
            for future in self.pending_api_requests.values():
                if not future.done():
                    future.set_exception(
                        HTTPException(status_code=503, detail="Userscript disconnected during request")
                    )
            self.pending_api_requests.clear()

            for queue in self.pending_api_streams.values():
                queue.put_nowait(None)  # 发送 None 作为结束信号
            self.pending_api_streams.clear()
            self.available_models = []  # 清空可用模型
        else:
            logger.info("一个非活动的 Userscript 连接已断开。")

    async def send_to_userscript(self, message: dict[str, Any]):
        if self.active_userscript and self.active_userscript.client_state == WebSocketState.CONNECTED:
            try:
                await self.active_userscript.send_json(message)
            except WebSocketDisconnect:
                self.disconnect_userscript(self.active_userscript)  # 传入当前 websocket
                raise HTTPException(status_code=503, detail="Userscript disconnected before message could be sent")
            except Exception as e:
                logger.error(f"发送消息到 Userscript 失败: {e}")
                self.disconnect_userscript(self.active_userscript)
                raise HTTPException(status_code=500, detail=f"Failed to send message to Userscript: {e}")
        else:
            logger.error("尝试发送消息，但没有活动的 Userscript 连接或连接已关闭")
            raise HTTPException(status_code=503, detail="Userscript not connected or connection closed")

    def set_userscript_info(self, api_key: str | None, models: list[dict[str, Any]]):
        if api_key:  # 只有当 apiKey 存在时才更新
            self.userscript_api_key = api_key
            logger.info(f"Userscript API Key 已设置/更新 (前5位): {api_key[:5]}...")
        self.available_models = models
        logger.info(f"从 Userscript 收到模型列表: {[m.get('id', 'N/A') for m in models]}")

    # --- AI Studio 相关方法 ---

    async def connect_aistudio_userscript(self, websocket: WebSocket, user_id: str = "default"):
        """连接 AI Studio 用户脚本"""
        await websocket.accept()

        # 如果已有连接，关闭旧连接
        if user_id in self.active_aistudio_userscripts:
            old_ws = self.active_aistudio_userscripts[user_id]
            try:
                await old_ws.close(code=4001, reason="New connection replaced this one")
            except Exception as e:
                logger.info(f"关闭旧 AI Studio 用户脚本连接时出错: {e}")

        self.active_aistudio_userscripts[user_id] = websocket
        logger.info(f"AI Studio 用户脚本已连接: {user_id}")

    def disconnect_aistudio_userscript(self, websocket: WebSocket, user_id: str = "default"):
        """断开 AI Studio 用户脚本连接"""
        if user_id in self.active_aistudio_userscripts and self.active_aistudio_userscripts[user_id] == websocket:
            del self.active_aistudio_userscripts[user_id]
            logger.info(f"AI Studio 用户脚本已断开: {user_id}")
        else:
            logger.info(f"一个非活动的 AI Studio 用户脚本连接已断开: {user_id}")

    async def send_to_aistudio_userscript(self, message: Dict[str, Any], user_id: str = "default"):
        """发送消息到 AI Studio 用户脚本"""
        if user_id not in self.active_aistudio_userscripts:
            logger.warning(f"AI Studio 用户脚本未连接: {user_id}")
            return

        websocket = self.active_aistudio_userscripts[user_id]
        if websocket.client_state != WebSocketState.CONNECTED:
            self.disconnect_aistudio_userscript(websocket, user_id)
            logger.warning(f"AI Studio 用户脚本连接已关闭: {user_id}")
            return

        try:
            message_str = json.dumps(message)
            await websocket.send_text(message_str)
            logger.debug(f"已发送消息到 AI Studio 用户脚本 {user_id}: {message.get('type', 'unknown')}")
        except WebSocketDisconnect:
            self.disconnect_aistudio_userscript(websocket, user_id)
            logger.warning(f"AI Studio 用户脚本在发送消息时断开连接: {user_id}")
        except Exception as e:
            logger.error(f"发送消息到 AI Studio 用户脚本失败: {e}")
            self.disconnect_aistudio_userscript(websocket, user_id)

    async def broadcast_to_userscripts(self, message: str):
        """广播消息到所有 AI Studio 用户脚本"""
        if not self.active_aistudio_userscripts:
            raise HTTPException(status_code=503, detail="没有活动的 AI Studio 用户脚本连接")

        disconnected_users = []

        for user_id, websocket in self.active_aistudio_userscripts.items():
            try:
                if websocket.client_state == WebSocketState.CONNECTED:
                    await websocket.send_text(message)
                else:
                    disconnected_users.append(user_id)
            except Exception as e:
                logger.error(f"广播消息到用户脚本 {user_id} 失败: {e}")
                disconnected_users.append(user_id)

        # 清理断开的连接
        for user_id in disconnected_users:
            if user_id in self.active_aistudio_userscripts:
                del self.active_aistudio_userscripts[user_id]
                logger.info(f"清理断开的用户脚本连接: {user_id}")

    def set_aistudio_models(self, models: List[Dict[str, Any]]):
        """设置 AI Studio 模型列表"""
        self.aistudio_models = models
        logger.info(f"AI Studio 模型列表已更新: {len(models)} 个模型")

    def get_aistudio_models(self) -> List[Dict[str, Any]]:
        """获取 AI Studio 模型列表"""
        return self.aistudio_models

    def add_message_handler(self, handler: Callable[[Dict[str, Any]], None]):
        """添加消息处理器"""
        self.message_handlers.append(handler)

    def handle_aistudio_message(self, message: Dict[str, Any]):
        """处理 AI Studio 消息"""
        for handler in self.message_handlers:
            try:
                handler(message)
            except Exception as e:
                logger.error(f"消息处理器执行失败: {e}")

    def has_aistudio_connections(self) -> bool:
        """检查是否有 AI Studio 连接"""
        return len(self.active_aistudio_userscripts) > 0

    def get_aistudio_connection_count(self) -> int:
        """获取 AI Studio 连接数量"""
        return len(self.active_aistudio_userscripts)


manager = ConnectionManager()
