import logging
from datetime import UTC, datetime  # UTC from datetime

from fastapi import (
    Depends,
    HTTPException,
)

from server.core import ModelD<PERSON>, ModelList, manager
from server.utils import verify_api_key

logger = logging.getLogger(__name__)


# --- API 端点 ---
async def list_models(_api_key: None = Depends(verify_api_key)):
    if not manager.active_userscript:
        raise HTTPException(status_code=503, detail="Userscript 未连接，无法获取模型列表。")
    if not manager.available_models:
        # 可能是 Userscript 刚连接，还没发 model list
        logger.warning("请求模型列表，但 Userscript 尚未提供。")
        # 可以尝试等待一小段时间，或者直接返回空，或者错误
        # 此处返回错误，让客户端重试
        raise HTTPException(status_code=503, detail="Userscript 已连接，但模型列表暂不可用，请稍后再试。")

    now = int(datetime.now(tz=UTC).timestamp())
    model_data_list = [
        ModelData(id=m.get("id", f"unknown-model-{i}"), created=now)
        for i, m in enumerate(manager.available_models)
        if m.get("id")  # 确保模型有 ID
    ]
    return ModelList(data=model_data_list)
