// ==UserScript==
// @name         AI Studio Proxy API - Simulate Submission
// @namespace    http://tampermonkey.net/
// @version      0.0.1
// @description  模拟在 Google AI Studio 页面提交信息的核心逻辑，并提供调试界面。
// <AUTHOR>
// @run-at       document-start
// @match        https://aistudio.google.com/*
// @grant        GM_xmlhttpRequest
// @connect      localhost
// @connect      alkalimakersuite-pa.clients6.google.com
// @noframes
// @grant        GM_cookie
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_registerMenuCommand
// @grant        GM_setClipboard
// ==/UserScript==

(function () {
  "use strict";
  console.log("AI Studio Proxy API 油猴脚本已加载并执行。");
  const SERVER_WS_URL = "ws://localhost:8000/ws/userscript";
  let interceptedModelsData = null;

  // 创建一个 Promise，用于在模型数据加载完成后解析
  let resolveModelsDataPromise;
  const modelsDataReadyPromise = new Promise((resolve) => {
    resolveModelsDataPromise = resolve;
  });

  const listModelsUrl =
    "https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/ListModels";
  const generateContentUrl =
    "https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/GenerateContent";

  const xhrOpen = XMLHttpRequest.prototype.open;
  const xhrSend = XMLHttpRequest.prototype.send; // 捕获原始的 send 方法

  XMLHttpRequest.prototype.open = function (method, url) {
    this._method = method; // 存储请求方法
    this._url = url; // 存储请求 URL
    return xhrOpen.apply(this, arguments);
  };

  XMLHttpRequest.prototype.send = function (body) {
    const xhr = this;
    if (xhr._url === listModelsUrl) {
      xhr.onload = function () {
        if (xhr.readyState === 4 && xhr.status === 200) {
          try {
            interceptedModelsData = JSON.parse(xhr.response);
            resolveModelsDataPromise();
          } catch (e) {
            console.error("解析模型数据失败:", e);
            resolveModelsDataPromise();
          }
        }
      };
    } else if (xhr._url === generateContentUrl) {
      xhr.onprogress = function () {
        let data = JSON.stringify(xhr.responseText);
        GM_xmlhttpRequest({
          method: "POST",
          url: "http://localhost:8000/aistudio/save_message",
          data: data,
          headers: { "Content-Type": "application/json" },
          onload: function (response) {
            console.log("消息已发送到本地服务器:", response.responseText);
          },
          onerror: function (error) {
            console.error("发送消息到本地服务器失败:", error);
          },
        });
      };
    }
    return xhrSend.apply(xhr, arguments); // 确保原始的 send 方法总是被调用
  };

  function createDebugPanel() {
    const debugPanel = document.createElement("div");
    debugPanel.id = "roo-debug-panel";
    debugPanel.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 350px;
            height: auto;
            max-height: 90vh;
            background-color: #282c34;
            border: 1px solid #61afef;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
            z-index: 99999;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #abb2bf;
            display: flex;
            flex-direction: column;
            resize: both;
            overflow: auto;
        `;

    const header = document.createElement("div");
    header.id = "roo-debug-panel-header";
    header.style.cssText = `
            padding: 10px 15px;
            background-color: #3e4451;
            border-bottom: 1px solid #61afef;
            cursor: grab;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: bold;
            color: #c678dd;
        `;
    header.innerHTML = `<span>AI Studio 调试面板</span>`;
    debugPanel.appendChild(header);

    const toggleButton = document.createElement("button");
    toggleButton.id = "roo-debug-toggle-button";
    toggleButton.textContent = "—";
    toggleButton.style.cssText = `
            background: none;
            border: none;
            color: #c678dd;
            font-size: 1.2em;
            cursor: pointer;
            outline: none;
            padding: 0 5px;
        `;
    header.appendChild(toggleButton);

    const content = document.createElement("div");
    content.id = "roo-debug-panel-content";
    content.style.cssText = `
            padding: 15px;
            flex-grow: 1;
            overflow-y: auto;
        `;
    debugPanel.appendChild(content);

    const messageSection = document.createElement("div");
    messageSection.className = "roo-debug-section";
    messageSection.innerHTML = `
            <label for="roo-debug-message-input" class="roo-debug-label">消息发送区:</label>
            <textarea id="roo-debug-message-input" class="roo-debug-input" rows="4" placeholder="输入要发送给AI Studio的消息..."></textarea>
            <button id="roo-debug-send-button" class="roo-debug-button">发送</button>
        `;
    content.appendChild(messageSection);

    const modelSection = document.createElement("div");
    modelSection.className = "roo-debug-section";
    modelSection.innerHTML = `
            <label for="roo-debug-model-select" class="roo-debug-label">模型选择:</label>
            <select id="roo-debug-model-select" class="roo-debug-select">
                <option value="">-- 请选择模型 --</option>
            </select>
        `;
    content.appendChild(modelSection);

    const paramsSection = document.createElement("div");
    paramsSection.className = "roo-debug-section";
    paramsSection.innerHTML = `
            <label class="roo-debug-label">参数设置:</label>
            <div class="roo-debug-param-group">
                <label for="roo-debug-temperature">温度 (Temperature):</label>
                <input type="number" id="roo-debug-temperature" class="roo-debug-input roo-debug-number-input" value="0.15" step="0.05" min="0" max="2">
            </div>
            <div class="roo-debug-param-group">
                <label for="roo-debug-top-p">Top P:</label>
                <input type="number" id="roo-debug-top-p" class="roo-debug-input roo-debug-number-input" value="0.95" step="0.05" min="0" max="1">
            </div>
            <div class="roo-debug-param-group">
                <label for="roo-debug-max-tokens">最大输出Token (Max Output Tokens):</label>
                <input type="number" id="roo-debug-max-tokens" class="roo-debug-input roo-debug-number-input" value="65536" step="1" min="1">
            </div>
            <div class="roo-debug-param-group">
                <label for="roo-debug-stop-sequences">停止序列 (Stop Sequences):</label>
                <textarea id="roo-debug-stop-sequences" class="roo-debug-input" rows="2" placeholder="每行一个停止序列，或用逗号分隔"></textarea>
            </div>
        `;
    content.appendChild(paramsSection);

    const logSection = document.createElement("div");
    logSection.className = "roo-debug-section";
    logSection.innerHTML = `
            <label for="roo-debug-log-output" class="roo-debug-label">操作日志/API响应:</label>
            <textarea id="roo-debug-log-output" class="roo-debug-input" rows="8" readonly></textarea>
        `;
    content.appendChild(logSection);

    document.body.appendChild(debugPanel);

    const style = document.createElement("style");
    style.textContent = `
            #roo-debug-panel { /* ... */ }
            #roo-debug-panel-header { /* ... */ }
            #roo-debug-panel-content { /* ... */ }
            .roo-debug-section { margin-bottom: 15px; padding-bottom: 15px; border-bottom: 1px dashed #4b5263; }
            .roo-debug-section:last-child { border-bottom: none; margin-bottom: 0; padding-bottom: 0; }
            .roo-debug-label { display: block; margin-bottom: 5px; font-size: 0.9em; color: #98c379; }
            .roo-debug-input, .roo-debug-select, .roo-debug-button { width: calc(100% - 10px); padding: 8px; margin-bottom: 10px; border-radius: 4px; border: 1px solid #5c6370; background-color: #3e4451; color: #abb2bf; font-size: 0.9em; box-sizing: border-box; }
            .roo-debug-input:focus, .roo-debug-select:focus { border-color: #61afef; outline: none; box-shadow: 0 0 0 2px rgba(97, 175, 239, 0.2); }
            .roo-debug-number-input { width: auto; min-width: 80px; }
            .roo-debug-button { background-color: #61afef; color: #ffffff; cursor: pointer; transition: background-color 0.2s ease; text-align: center; width: auto; padding: 8px 15px; margin-right: 10px; }
            .roo-debug-button:hover { background-color: #569cd6; }
            .roo-debug-param-group { display: flex; align-items: center; margin-bottom: 10px; }
            .roo-debug-param-group label { flex-shrink: 0; margin-right: 10px; min-width: 120px; font-size: 0.9em; color: #98c379; }
            .roo-debug-param-group input, .roo-debug-param-group textarea { flex-grow: 1; margin-bottom: 0; }
            #roo-debug-log-output { background-color: #1e2127; border: 1px solid #4b5263; font-family: 'Consolas', 'Monaco', monospace; font-size: 0.8em; height: 150px; resize: vertical; }
        `;
    document.head.appendChild(style);

    let isDragging = false;
    let offsetX, offsetY;
    header.addEventListener("mousedown", (e) => {
      isDragging = true;
      offsetX = e.clientX - debugPanel.getBoundingClientRect().left;
      offsetY = e.clientY - debugPanel.getBoundingClientRect().top;
      debugPanel.style.cursor = "grabbing";
      // 阻止文本选择
      e.preventDefault();
    });
    document.addEventListener("mousemove", (e) => {
      if (!isDragging) return;
      debugPanel.style.left = e.clientX - offsetX + "px";
      debugPanel.style.top = e.clientY - offsetY + "px";
    });
    document.addEventListener("mouseup", () => {
      if (isDragging) {
        isDragging = false;
        debugPanel.style.cursor = "grab";
      }
    });

    let isMinimized = false;
    let originalHeight = debugPanel.style.height;
    let originalResize = debugPanel.style.resize;
    toggleButton.addEventListener("click", () => {
      if (isMinimized) {
        // 恢复
        content.style.display = "block";
        debugPanel.style.height = originalHeight;
        debugPanel.style.resize = originalResize;
        toggleButton.textContent = "—";
        isMinimized = false;
      } else {
        // 最小化
        originalHeight = debugPanel.style.height; // 保存当前高度
        originalResize = debugPanel.style.resize;
        content.style.display = "none";
        debugPanel.style.height = header.offsetHeight + "px"; // 只显示头部
        debugPanel.style.resize = "none"; // 禁用调整大小
        toggleButton.textContent = "□"; // 变成最大化按钮
        isMinimized = true;
      }
    });
    setTimeout(() => {
      if (!isMinimized) {
        toggleButton.click();
      }
    }, 0);

    const sendMessageButton = document.getElementById("roo-debug-send-button");
    const messageInput = document.getElementById("roo-debug-message-input");
    const logOutput = document.getElementById("roo-debug-log-output");

    function logToDebugPanel(message, type = "info") {
      const timestamp = new Date().toLocaleTimeString();
      let prefix = "";
      // let color = "#abb2bf"; // default color not used directly here

      switch (type) {
        case "info":
          prefix = "[信息]";
          // color = "#61afef";
          break;
        case "success":
          prefix = "[成功]";
          // color = "#98c379";
          break;
        case "warning":
          prefix = "[警告]";
          // color = "#e5c07b";
          break;
        case "error":
          prefix = "[错误]";
          // color = "#e06c75";
          break;
      }

      logOutput.value += `[${timestamp}] ${prefix}: ${message}\n`;
      logOutput.scrollTop = logOutput.scrollHeight;
    }

    function populateModelSelect(modelsDataToPopulate) {
      const modelSelect = document.getElementById("roo-debug-model-select");
      if (modelSelect) {
        modelSelect.innerHTML = '<option value="">-- 请选择模型 --</option>';
        if (
          modelsDataToPopulate &&
          modelsDataToPopulate[0] &&
          Array.isArray(modelsDataToPopulate[0])
        ) {
          modelsDataToPopulate[0].forEach((model) => {
            if (model && model.length > 3) {
              // Ensure model array and its elements exist
              const modelId = model[0];
              const modelName = model[3];
              if (modelId && modelName) {
                const option = document.createElement("option");
                option.value = modelId;
                option.textContent = modelName;
                modelSelect.appendChild(option);
              }
            } else {
              console.warn("Skipping invalid model data entry:", model);
            }
          });
          logToDebugPanel("模型列表已更新。", "success");
          // 设置默认模型
          const defaultModelIdToSet = "models/gemini-2.5-pro-preview-05-06";
          let modelExistsInSelect = false;
          if (modelSelect && modelSelect.options) {
            // 确保 modelSelect 和 options 存在
            for (let i = 0; i < modelSelect.options.length; i++) {
              if (modelSelect.options[i].value === defaultModelIdToSet) {
                modelExistsInSelect = true;
                break;
              }
            }
          }

          if (modelExistsInSelect) {
            modelSelect.value = defaultModelIdToSet;
            // 延迟调用，确保事件监听器已附加并且页面稳定
            // 并且确保 setAIStudioModel 能够正确执行
            setTimeout(() => {
              const currentSelectedModelElement = document.getElementById(
                "roo-debug-model-select"
              );
              if (
                currentSelectedModelElement &&
                currentSelectedModelElement.value === defaultModelIdToSet
              ) {
                // 只有当值仍然是期望的默认值时才触发，避免用户在延迟期间手动更改
                const changeEvent = new Event("change", { bubbles: true });
                currentSelectedModelElement.dispatchEvent(changeEvent);
                logToDebugPanel(
                  `默认模型已自动设置为 ${defaultModelIdToSet}。`,
                  "info"
                );
              } else if (currentSelectedModelElement) {
                logToDebugPanel(
                  `默认模型 ${defaultModelIdToSet} 在延迟后未被选中 (当前: ${currentSelectedModelElement.value})，可能是用户已更改。`,
                  "info"
                );
              } else {
                logToDebugPanel(`警告: 延迟后未找到模型选择元素。`, "warning");
              }
            }, 500); // 稍作延迟以确保其他初始化完成
          } else {
            logToDebugPanel(
              `警告: 预设的默认模型 ${defaultModelIdToSet} 在列表中未找到。`,
              "warning"
            );
          }
        } else {
          logToDebugPanel(
            "模型数据格式不正确或为空，无法填充模型列表。",
            "warning"
          );
          console.warn(
            "Invalid or empty modelsData for populateModelSelect:",
            modelsDataToPopulate
          );
        }
      } else {
        console.warn("未找到模型选择下拉框 #roo-debug-model-select");
      }
    }

    if (interceptedModelsData) {
      populateModelSelect(interceptedModelsData);
    } else {
      logToDebugPanel("等待模型数据加载...", "info");
    }

    async function sendMessageToAIStudio() {
      const message = messageInput.value.trim();

      if (!message) {
        logToDebugPanel("请输入要发送的消息。", "warning");
        return;
      }

      logToDebugPanel("正在发送消息...", "info");
      // 编辑消息时，可以从这里找到：ms-prompt-chunk>ms-text-chunk>ms-autosize-textarea>textarea
      try {
        let aiStudioInput = document.querySelector(
          'textarea[aria-label="输入消息"], textarea[aria-label="Message input"], textarea[placeholder*="Enter a prompt here"], textarea[placeholder*="Send a message"]'
        );
        if (!aiStudioInput) {
          aiStudioInput = document.querySelector(
            'div[contenteditable="true"][role="textbox"]'
          );
        }
        if (!aiStudioInput) {
          aiStudioInput = document.querySelector(
            "textarea:not([readonly]):not([disabled])"
          ); // More generic, less specific
        }
        if (!aiStudioInput) {
          aiStudioInput = document.querySelector(
            'div[contenteditable="true"]:not([aria-disabled="true"])'
          );
        }

        if (!aiStudioInput) {
          throw new Error(
            "未找到 AI Studio 页面的消息输入框。请检查页面结构或更新选择器。"
          );
        }

        if (aiStudioInput.tagName === "TEXTAREA") {
          aiStudioInput.value = message;
          aiStudioInput.dispatchEvent(new Event("input", { bubbles: true }));
          aiStudioInput.dispatchEvent(new Event("change", { bubbles: true })); // Some frameworks listen to change
        } else if (aiStudioInput.contentEditable === "true") {
          aiStudioInput.textContent = message;
          aiStudioInput.dispatchEvent(new Event("input", { bubbles: true }));
          // Focus and simulate typing for some rich text editors
          aiStudioInput.focus();
          document.execCommand("insertText", false, " "); // Hack to trigger update in some editors
          aiStudioInput.textContent = message; // Re-set after potential execCommand side-effects
          aiStudioInput.dispatchEvent(
            new KeyboardEvent("keyup", { bubbles: true, key: " " })
          );
        } else {
          throw new Error("AI Studio 输入框类型不支持。");
        }

        // Try to find a send button first
        let sendButton = document.querySelector(
          'button[aria-label*="Send"], button[aria-label*="Submit"], button[data-testid*="send"], button > span[class*="send"]'
        );
        if (sendButton && sendButton.tagName !== "BUTTON")
          sendButton = sendButton.closest("button"); // If we found a span inside a button

        if (sendButton && !sendButton.disabled) {
          sendButton.click();
          logToDebugPanel("消息通过点击发送按钮发送！", "success");
        } else {
          // Fallback to Ctrl/Meta + Enter if button not found or not suitable
          const keyEvent = new KeyboardEvent("keydown", {
            key: "Enter",
            code: "Enter",
            which: 13,
            keyCode: 13,
            bubbles: true,
            cancelable: true,
            ctrlKey: true,
            metaKey: true,
          });
          aiStudioInput.dispatchEvent(keyEvent);
          logToDebugPanel("消息通过模拟 Ctrl/Meta+Enter 发送！", "success");
        }

        messageInput.value = "";
      } catch (error) {
        logToDebugPanel(`发送失败: ${error.message}`, "error");
        console.error("AI Studio 消息发送错误:", error);
      }
    }
    sendMessageButton.addEventListener("click", sendMessageToAIStudio);

    const temperatureInput = document.getElementById("roo-debug-temperature");
    const topPInput = document.getElementById("roo-debug-top-p"); // 新增 Top P 输入框
    const maxTokensInput = document.getElementById("roo-debug-max-tokens");
    const stopSequencesInput = document.getElementById(
      "roo-debug-stop-sequences"
    );
    const modelSelectDropdown = document.getElementById(
      "roo-debug-model-select"
    ); // 获取模型选择下拉框

    /**
     * 在 AI Studio 页面上切换模型。
     * @param {string} modelId - 要切换到的模型 ID (例如 "gemini-1.5-pro-latest")。
     */
    async function setAIStudioModel(modelId) {
      if (!modelId) {
        logToDebugPanel("未选择有效模型进行切换。", "warning");
        return;
      }
      logToDebugPanel(`正在尝试将 AI Studio 模型切换为 ${modelId}...`, "info");

      try {
        // 1. 找到 AI Studio 页面的模型选择器并点击以展开选项
        //    根据 aistudio.html, data-test-ms-model-selector 似乎是主选择器
        const studioModelSelector = document.querySelector(
          "mat-select[data-test-ms-model-selector]"
        );
        if (!studioModelSelector) {
          throw new Error("未找到 AI Studio 页面的模型选择器。");
        }
        studioModelSelector.click(); // 点击以打开下拉菜单
        await new Promise((resolve) => setTimeout(resolve, 500)); // 等待下拉菜单动画

        // 2. 从 interceptedModelsData 中找到 modelId 对应的 displayName
        let modelDisplayName = "";
        if (
          interceptedModelsData &&
          interceptedModelsData[0] &&
          Array.isArray(interceptedModelsData[0])
        ) {
          const modelEntry = interceptedModelsData[0].find(
            (m) => m[0] && m[0].endsWith(modelId)
          );
          if (modelEntry && modelEntry[3]) {
            modelDisplayName = modelEntry[3];
          }
        }
        if (!modelDisplayName) {
          // 如果在劫持数据中找不到，尝试直接使用 modelId（可能不完美，但作为后备）
          modelDisplayName = modelId.includes("/")
            ? modelId.split("/")[1]
            : modelId;
          modelDisplayName = modelDisplayName
            .replace(/-/g, " ")
            .replace(/\b\w/g, (l) => l.toUpperCase()); // 尝试格式化
          logToDebugPanel(
            `警告: 未在劫持数据中找到 ${modelId} 的确切显示名称，将尝试使用 '${modelDisplayName}'。`,
            "warning"
          );
        }

        // 3. 在展开的选项中找到包含目标模型显示名称的 mat-option
        //    选项通常在 cdk-overlay-pane 内
        const optionsPane = document.querySelector("div.cdk-overlay-pane");
        if (!optionsPane) {
          // 有时选项面板可能需要更长时间渲染，或者选择器可能需要调整
          await new Promise((resolve) => setTimeout(resolve, 1000)); // 额外等待
          const optionsPaneRetry = document.querySelector(
            "div.cdk-overlay-pane"
          );
          if (!optionsPaneRetry) {
            throw new Error("未找到模型选项面板 (cdk-overlay-pane)。");
          }
        }

        // 尝试更精确地定位包含模型名称的 span
        // 假设模型名称在 mat-option -> model-option-content -> span.gmat-body-medium 结构中
        let targetOption = null;
        const allOptions = document.querySelectorAll(
          'div.cdk-overlay-pane mat-option, div.cdk-overlay-pane div[role="option"]'
        );

        logToDebugPanel(`尝试匹配显示名称: "${modelDisplayName}"`, "info");

        for (const option of allOptions) {
          const optionTextElement = option.querySelector(
            ".model-option-content span.gmat-body-medium, .mdc-list-item__primary-text"
          ); // 适配不同可能的内部结构
          if (optionTextElement) {
            const text = optionTextElement.textContent.trim();
            // logToDebugPanel(`检查选项: "${text}"`, "info"); // 日志过多，可按需开启
            if (
              text.includes(modelDisplayName) ||
              modelDisplayName.includes(text)
            ) {
              // 允许部分匹配或反向包含
              targetOption = option;
              logToDebugPanel(
                `找到匹配选项: "${text}" for "${modelDisplayName}"`,
                "success"
              );
              break;
            }
          }
        }

        if (!targetOption) {
          // 如果精确匹配失败，尝试模糊匹配，移除版本号等
          const simplifiedModelDisplayName = modelDisplayName
            .replace(/[\d.-]+$/, "")
            .trim(); // 移除末尾版本号
          for (const option of allOptions) {
            const optionTextElement = option.querySelector(
              ".model-option-content span.gmat-body-medium, .mdc-list-item__primary-text"
            );
            if (optionTextElement) {
              const text = optionTextElement.textContent.trim();
              if (
                text
                  .toLowerCase()
                  .includes(simplifiedModelDisplayName.toLowerCase())
              ) {
                targetOption = option;
                logToDebugPanel(
                  `通过模糊匹配找到选项: "${text}" for simplified "${simplifiedModelDisplayName}"`,
                  "success"
                );
                break;
              }
            }
          }
        }

        if (!targetOption) {
          logToDebugPanel(
            `在选项中未找到模型 "${modelDisplayName}"。可用选项:`,
            "error"
          );
          allOptions.forEach((opt) => {
            const optTextEl = opt.querySelector(
              ".model-option-content span.gmat-body-medium, .mdc-list-item__primary-text"
            );
            if (optTextEl)
              logToDebugPanel(` - ${optTextEl.textContent.trim()}`, "info");
          });
          // 尝试关闭下拉框，避免页面卡住
          studioModelSelector.click(); // 再次点击主选择器尝试关闭
          throw new Error(`在选项中未找到模型 "${modelDisplayName}"。`);
        }

        // 4. 点击找到的选项
        targetOption.click();
        await new Promise((resolve) => setTimeout(resolve, 300)); // 等待选择生效

        logToDebugPanel(
          `AI Studio 模型已成功切换为 ${modelDisplayName} (ID: ${modelId})。`,
          "success"
        );
      } catch (error) {
        logToDebugPanel(`切换 AI Studio 模型失败: ${error.message}`, "error");
        console.error("AI Studio 模型切换错误:", error);
      }
    }

    /**
     * 模拟在 AI Studio 页面设置参数。
     * @param {string} paramName - 参数名称 (e.g., "temperature", "maxTokens", "stopSequences", "topP")。
     * @param {any} value - 要设置的参数值。
     */
    async function setAIStudioParameter(paramName, value) {
      logToDebugPanel(`正在设置 ${paramName} 为 ${value}...`, "info");
      try {
        let targetElement;
        // server.py 中的选择器作为参考
        // TEMPERATURE_INPUT_SELECTOR = 'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input'
        // MAX_OUTPUT_TOKENS_SELECTOR = 'input[aria-label="Maximum output tokens"]'
        // STOP_SEQUENCE_INPUT_SELECTOR = 'input[aria-label="Add stop token"]'
        // TOP_P_INPUT_SELECTOR = 'div.settings-item-column:has(h3:text-is("Top P")) input[type="number"].slider-input'

        switch (paramName) {
          case "temperature":
            targetElement = document.querySelector(
              'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input'
            );
            if (!targetElement) {
              // Fallback if specific selector fails
              const labels = Array.from(
                document.querySelectorAll("h3.gmat-body-medium")
              );
              for (const label of labels) {
                if (label.textContent.trim().toLowerCase() === "temperature") {
                  targetElement = label
                    .closest(".settings-item-column")
                    ?.querySelector('input[type="number"].slider-input');
                  if (targetElement) break;
                }
              }
            }
            if (targetElement) {
              targetElement.value = value;
              targetElement.dispatchEvent(
                new Event("input", { bubbles: true })
              );
              targetElement.dispatchEvent(
                new Event("change", { bubbles: true })
              );
            }
            break;
          case "topP": // 新增 Top P 处理
            // 查找所有 h3.gmat-body-medium 元素
            const allH3s = document.querySelectorAll("h3.gmat-body-medium");
            let topPLabel = null;
            for (const h3 of allH3s) {
              if (h3.textContent.trim().toLowerCase() === "top p") {
                topPLabel = h3;
                break;
              }
            }

            if (topPLabel) {
              const parentColumn = topPLabel.closest(".settings-item-column");
              if (parentColumn) {
                targetElement = parentColumn.querySelector(
                  'input[type="number"].slider-input'
                );
              }
            }

            if (targetElement) {
              targetElement.value = value;
              targetElement.dispatchEvent(
                new Event("input", { bubbles: true })
              );
              targetElement.dispatchEvent(
                new Event("change", { bubbles: true })
              );
            } else {
              // 保留原始的 fallback 逻辑，以防万一
              const labels = Array.from(
                document.querySelectorAll("h3.gmat-body-medium")
              );
              for (const label of labels) {
                if (label.textContent.trim().toLowerCase() === "top p") {
                  targetElement = label
                    .closest(".settings-item-column")
                    ?.querySelector('input[type="number"].slider-input');
                  if (targetElement) {
                    targetElement.value = value;
                    targetElement.dispatchEvent(
                      new Event("input", { bubbles: true })
                    );
                    targetElement.dispatchEvent(
                      new Event("change", { bubbles: true })
                    );
                    break;
                  }
                }
              }
            }
            break;
          case "maxOutputTokens":
            targetElement = document.querySelector(
              'input[aria-label="Maximum output tokens"]'
            );
            if (!targetElement) {
              // Fallback
              const labels = Array.from(
                document.querySelectorAll("h3.gmat-body-medium")
              );
              for (const label of labels) {
                if (
                  label.textContent.trim().toLowerCase() === "output length"
                ) {
                  // "Output length" seems to be the label for max tokens
                  targetElement = label
                    .closest(".settings-item-column, .settings-item")
                    ?.querySelector('input[type="number"]');
                  if (targetElement) break;
                }
              }
            }
            if (targetElement) {
              targetElement.value = value;
              targetElement.dispatchEvent(
                new Event("input", { bubbles: true })
              );
              targetElement.dispatchEvent(
                new Event("change", { bubbles: true })
              );
            }
            break;
          case "stopSequences":
            // 停止序列比较特殊，它是一个chips input
            const stopInput = document.querySelector(
              'input[aria-label="Add stop token"]'
            );
            const existingChips = document.querySelectorAll(
              'mat-chip-grid mat-chip-row button[aria-label*="Remove"]'
            );

            // 1. 清除现有停止序列
            for (const chipRemoveButton of existingChips) {
              chipRemoveButton.click();
              await new Promise((resolve) => setTimeout(resolve, 50)); // 短暂等待移除生效
            }

            // 2. 添加新的停止序列
            if (stopInput && value && Array.isArray(value)) {
              for (const seq of value) {
                if (seq.trim()) {
                  stopInput.value = seq.trim();
                  stopInput.dispatchEvent(
                    new Event("input", { bubbles: true })
                  );
                  // 模拟回车添加
                  const enterEvent = new KeyboardEvent("keydown", {
                    key: "Enter",
                    code: "Enter",
                    bubbles: true,
                  });
                  stopInput.dispatchEvent(enterEvent);
                  await new Promise((resolve) => setTimeout(resolve, 100)); // 等待chip添加
                }
              }
              targetElement = stopInput; // 标记为已处理
            } else if (stopInput && !value) {
              // 如果value为空数组或null，表示清空
              targetElement = stopInput; // 标记为已处理 (清空操作已完成)
            }
            break;
          default:
            logToDebugPanel(`未知参数: ${paramName}`, "warning");
            return;
        }

        if (targetElement) {
          logToDebugPanel(`${paramName} 设置成功！`, "success");
        } else {
          logToDebugPanel(
            `未找到 AI Studio 页面上 ${paramName} 对应的DOM元素。`,
            "error"
          );
        }
      } catch (error) {
        logToDebugPanel(`设置 ${paramName} 失败: ${error.message}`, "error");
        console.error(`AI Studio 参数设置错误 (${paramName}):`, error);
      }
    }

    // 添加事件监听器
    modelSelectDropdown.addEventListener("change", (event) => {
      setAIStudioModel(event.target.value);
    });

    temperatureInput.addEventListener("change", (event) => {
      setAIStudioParameter("temperature", parseFloat(event.target.value));
    });
    temperatureInput.addEventListener("input", (event) => {
      // 实时更新滑块类输入
      setAIStudioParameter("temperature", parseFloat(event.target.value));
    });

    topPInput.addEventListener("change", (event) => {
      // 新增 Top P 监听
      setAIStudioParameter("topP", parseFloat(event.target.value));
    });
    topPInput.addEventListener("input", (event) => {
      // 新增 Top P 监听
      setAIStudioParameter("topP", parseFloat(event.target.value));
    });

    maxTokensInput.addEventListener("change", (event) => {
      setAIStudioParameter("maxOutputTokens", parseInt(event.target.value, 10));
    });
    maxTokensInput.addEventListener("input", (event) => {
      setAIStudioParameter("maxOutputTokens", parseInt(event.target.value, 10));
    });

    stopSequencesInput.addEventListener("change", (event) => {
      const sequences = event.target.value
        .split(/[\n,]/)
        .map((s) => s.trim())
        .filter((s) => s.length > 0);
      setAIStudioParameter("stopSequences", sequences);
    });
    async function syncAllParamsToAIStudioPage() {
      logToDebugPanel("开始将调试面板参数同步到 AI Studio 页面...", "info");

      const modelSelect = document.getElementById("roo-debug-model-select");
      const temperatureInput = document.getElementById("roo-debug-temperature");
      const topPInput = document.getElementById("roo-debug-top-p");
      const maxTokensInput = document.getElementById("roo-debug-max-tokens");

      if (!modelSelect || !temperatureInput || !topPInput || !maxTokensInput) {
        logToDebugPanel(
          "错误：一个或多个调试面板参数输入框未找到，无法同步。",
          "error"
        );
        return;
      }

      // 等待调试面板参数初始化（如默认模型选择和其事件处理）
      // populateModelSelect 内部有 setTimeout(..., 500)
      // modelSelect.onchange -> setAIStudioModel 内部有 setTimeout(..., 500) 和 setTimeout(..., 300)
      logToDebugPanel("等待调试面板参数初始化完成...", "info");
      await new Promise((resolve) => setTimeout(resolve, 2000)); // 给予2秒确保前置异步操作完成

      const modelToSet = modelSelect.value;
      const tempToSet = parseFloat(temperatureInput.value);
      const topPToSet = parseFloat(topPInput.value);
      const maxTokensToSet = parseInt(maxTokensInput.value, 10);

      logToDebugPanel(
        `准备同步以下参数: 模型ID='${
          modelToSet || "未选择"
        }', 温度=${tempToSet}, TopP=${topPToSet}, MaxTokens=${maxTokensToSet}`,
        "info"
      );

      if (modelToSet) {
        await setAIStudioModel(modelToSet); // 此函数内部包含日志
      } else {
        logToDebugPanel("未在调试面板中选择模型，跳过模型同步。", "info");
      }

      // 如果模型被设置（或尝试设置），等待一段时间让页面上的模型切换效果（可能包括参数重置）完成
      if (modelToSet) {
        logToDebugPanel("等待模型切换在AI Studio页面上生效...", "info");
        await new Promise((resolve) => setTimeout(resolve, 1500));
      }

      if (!isNaN(tempToSet)) {
        await setAIStudioParameter("temperature", tempToSet); // 此函数内部包含日志
      } else {
        logToDebugPanel(
          `调试面板中的温度值 (${temperatureInput.value}) 无效，跳过温度同步。`,
          "warning"
        );
      }

      if (!isNaN(topPToSet)) {
        await setAIStudioParameter("topP", topPToSet); // 此函数内部包含日志
      } else {
        logToDebugPanel(
          `调试面板中的Top P值 (${topPInput.value}) 无效，跳过Top P同步。`,
          "warning"
        );
      }

      if (!isNaN(maxTokensToSet) && maxTokensToSet > 0) {
        await setAIStudioParameter("maxOutputTokens", maxTokensToSet); // 此函数内部包含日志
      } else {
        logToDebugPanel(
          `调试面板中的Max Tokens值 (${maxTokensInput.value}) 无效或小于1，跳过Max Tokens同步。`,
          "warning"
        );
      }

      logToDebugPanel(
        "调试面板参数到AI Studio页面的同步操作已全部尝试执行。",
        "success"
      );
    }

    // 在createDebugPanel函数的末尾，确保所有面板元素已创建且事件监听器已附加。
    // 使用setTimeout确保populateModelSelect内部的异步操作（如设置默认模型和触发事件）有时间完成。
    logToDebugPanel("计划在2.5秒后执行初始参数同步到AI Studio页面...", "info");
    setTimeout(async () => {
      if (document.getElementById("roo-debug-panel")) {
        // 再次检查面板是否还存在
        logToDebugPanel("开始执行延迟的初始参数同步...", "info");
        await syncAllParamsToAIStudioPage();
      } else {
        logToDebugPanel(
          "调试面板在计划的初始参数同步执行前已被移除，取消同步。",
          "warning"
        );
      }
    }, 2500); // 2.5秒延迟，确保页面和调试面板都稳定
  }

  const domReadyPromise = new Promise((resolve) => {
    if (
      document.readyState === "interactive" ||
      document.readyState === "complete"
    ) {
      resolve();
    } else {
      document.addEventListener("DOMContentLoaded", resolve, { once: true });
    }
  });

  Promise.all([modelsDataReadyPromise, domReadyPromise])
    .then(() => {
      console.log("模型数据和 DOM 都已准备好，开始创建调试面板。");
      createDebugPanel();
    })
    .catch((error) => {
      console.error("初始化调试面板失败:", error);
    });
})();
