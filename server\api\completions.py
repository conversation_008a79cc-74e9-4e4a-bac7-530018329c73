import asyncio
import json
import logging
import os
import time
import uuid
from typing import Any

from fastapi import (
    Depends,
    HTTPException,
)
from fastapi.responses import StreamingResponse

from server.core import ChatCompletionRequest, ContentItem, Message, manager
from server.utils import verify_api_key

logger = logging.getLogger(__name__)


# --- API Key 认证 ---
SERVER_OWN_API_KEY = os.environ.get("API_KEY")


def prepare_prompt_and_files_for_userscript(
    messages: list[Message],
) -> tuple[str, list[dict[str, str]], list[str] | None]:
    """
    从 OpenAI 格式的 messages 中提取最后一个用户消息的文本和图片作为 prompt 和 files。
    并尝试构建一个简单的对话历史作为 prompt (如果最后一个消息不含文本)。
    返回: (prompt_text, files_to_upload, chat_history_for_prompt)
    chat_history_for_prompt 仅用于当 prompt_text 为空时，作为备用 prompt。
    """
    prompt_text = ""
    files_to_upload: list[dict[str, str]] = []

    # 简单的对话历史构建，仅用于当最后一个用户消息没有文本时
    # 注意：这不会被 UserScript 用于构建 Gemini 的 chat_metadata，仅用于生成当前轮的 prompt
    simple_chat_history_parts = []

    # 优先处理最后一个用户消息的内容
    last_user_message_content: str | list[ContentItem] | None = None
    for msg in reversed(messages):
        if msg.role == "user":
            last_user_message_content = msg.content
            break
        # 记录非用户消息，用于构建简单历史
        if isinstance(msg.content, str):
            simple_chat_history_parts.append(f"{msg.role.capitalize()}: {msg.content}")
        elif isinstance(msg.content, list):
            for item in msg.content:
                if item.type == "text" and item.text:
                    simple_chat_history_parts.append(f"{msg.role.capitalize()}: {item.text}")

    if last_user_message_content:
        if isinstance(last_user_message_content, str):
            prompt_text = last_user_message_content
        elif isinstance(last_user_message_content, list):
            for item in last_user_message_content:
                if item.type == "text" and item.text:
                    prompt_text += item.text + "\n"  # 多段文本合并
                elif item.type == "image_url" and item.image_url and item.image_url.url.startswith("data:image"):
                    # 提取 base64 数据和文件类型
                    try:
                        header, base64_data = item.image_url.url.split(",", 1)
                        mime_type = header.split(":")[1].split(";")[0]  # e.g., "image/png"
                        extension = mime_type.split("/")[1] if "/" in mime_type else "png"
                        file_name = f"image_{len(files_to_upload) + 1}.{extension}"
                        files_to_upload.append({"name": file_name, "base64": item.image_url.url})  # 传递完整的 data URL
                    except Exception as e:
                        logger.error(f"解析图片 Data URL 失败: {item.image_url.url[:50]}... Error: {e}")
            prompt_text = prompt_text.strip()

    # 如果从最后一个用户消息中没有提取到文本，但有历史消息，则用历史消息作为 prompt
    if not prompt_text and simple_chat_history_parts:
        # 反转历史记录，使其按时间顺序
        prompt_text = "\n".join(reversed(simple_chat_history_parts))
        logger.info("使用构建的对话历史作为 prompt，因为最后一个用户消息无文本。")

    # 如果最终 prompt_text 仍然为空，但有图片，则使用通用提示
    if not prompt_text and files_to_upload:
        prompt_text = "请描述这些图片。"  # 或者 "Analyze these images."

    logger.info(f"为 Userscript 准备的 Prompt (前50字符): '{prompt_text[:50]}...', 文件数量: {len(files_to_upload)}")
    return prompt_text, files_to_upload, None  # chat_metadata 暂不通过此函数处理


async def create_chat_completion(request: ChatCompletionRequest, _api_key: None = Depends(verify_api_key)):
    if not manager.active_userscript:
        raise HTTPException(status_code=503, detail="Userscript 未连接，无法处理请求。")

    request_id = str(uuid.uuid4())

    # 从 request.messages 提取 prompt 和 files
    # chat_metadata 应该从请求中获取，如果客户端支持传递 (例如通过 request.gemini_chat_metadata)
    # 这里我们假设每次都是新对话，所以 chat_metadata 为 None
    prompt_text, files_for_userscript, _ = prepare_prompt_and_files_for_userscript(request.messages)
    # gemini_chat_metadata = request.gemini_chat_metadata # 如果 ChatCompletionRequest 中定义了此字段

    if not prompt_text:  # 即使有图片，也应该有一个基本提示
        raise HTTPException(status_code=400, detail="无法从消息中提取有效的文本提示，或生成默认提示。")

    payload_to_userscript: dict[str, Any] = {
        "model": request.model,
        "prompt": prompt_text,
        "stream": request.stream or False,  # 确保有默认值
        "files": files_for_userscript,
        # "chat_metadata": gemini_chat_metadata, # 如果需要传递
    }

    if request.stream:
        queue: asyncio.Queue[dict[str, Any] | None] = asyncio.Queue()
        manager.pending_api_streams[request_id] = queue

        try:
            await manager.send_to_userscript(
                {"type": "api_request", "request_id": request_id, "payload": payload_to_userscript}
            )
        except HTTPException as e:  # send_to_userscript 可能会抛出
            manager.pending_api_streams.pop(request_id, None)
            raise e  # 原样抛出

        async def stream_generator():
            completion_id = f"chatcmpl-{uuid.uuid4()}"
            created_time = int(time.time())
            # last_metadata = None # 用于在 DONE 消息中包含最终的 metadata

            # 第一个块：角色信息
            initial_chunk: dict[str, Any] = {
                "id": completion_id,
                "object": "chat.completion.chunk",
                "created": created_time,
                "model": request.model,
                "choices": [{"index": 0, "delta": {"role": "assistant"}, "finish_reason": None}],
            }
            yield f"data: {json.dumps(initial_chunk)}\n\n"

            try:
                while True:
                    chunk_data = await queue.get()  # 等待 UserScript 的数据块
                    if chunk_data is None:  # 结束信号
                        break

                    # if chunk_data.get("metadata"): # 记录元数据
                    #     last_metadata = chunk_data.get("metadata")

                    if "error" in chunk_data:
                        logger.error(f"流式响应错误 (来自Userscript): {chunk_data['error']}")
                        # OpenAI 错误格式建议在 content 中包含错误信息
                        error_content = f"\n\n[Userscript Error: {chunk_data['error']}]"
                        error_chunk: dict[str, Any] = {
                            "id": completion_id,
                            "object": "chat.completion.chunk",
                            "created": created_time,
                            "model": request.model,
                            "choices": [
                                {
                                    "index": 0,
                                    "delta": {"content": error_content},  # 错误信息放入 content
                                    "finish_reason": "error",  # 标记完成原因为 error
                                }
                            ],
                        }
                        yield f"data: {json.dumps(error_chunk)}\n\n"
                        break  # 出错后也结束流

                    # 正常的 delta 数据块
                    response_chunk: dict[str, Any] = {
                        "id": completion_id,
                        "object": "chat.completion.chunk",
                        "created": created_time,
                        "model": request.model,
                        "choices": [
                            {
                                "index": 0,
                                "delta": chunk_data.get("delta", {}),  # UserScript 应直接提供 delta 结构
                                "finish_reason": chunk_data.get("finish_reason"),  # UserScript 可能在最后一块提供
                            }
                        ],
                    }
                    yield f"data: {json.dumps(response_chunk)}\n\n"
                    if chunk_data.get("finish_reason"):
                        break
            except Exception as e:
                logger.error(f"流生成器内部错误: {e}", exc_info=True)
                # 尝试发送一个错误块给客户端
                error_content = f"\n\n[Server Error during stream: {e}]"
                error_chunk_final: dict[str, Any] = {
                    "id": completion_id,
                    "object": "chat.completion.chunk",
                    "created": created_time,
                    "model": request.model,
                    "choices": [{"index": 0, "delta": {"content": error_content}, "finish_reason": "error"}],
                }
                yield f"data: {json.dumps(error_chunk_final)}\n\n"
            finally:
                manager.pending_api_streams.pop(request_id, None)  # 确保从挂起中移除
                logger.info(f"流式响应 {request_id} 结束。")

            # 最终的 DONE 消息
            # done_payload = {"metadata": last_metadata} if last_metadata else {} # 可选：在DONE中附加元数据
            yield "data: [DONE]\n\n"

        return StreamingResponse(stream_generator(), media_type="text/event-stream")

    else:  # 非流式
        future: asyncio.Future[Any] = asyncio.Future()
        manager.pending_api_requests[request_id] = future

        try:
            await manager.send_to_userscript(
                {"type": "api_request", "request_id": request_id, "payload": payload_to_userscript}
            )
        except HTTPException as e:
            manager.pending_api_requests.pop(request_id, None)
            raise e

        try:
            # 增加超时时间，例如 5 分钟 (300秒)
            result_data = await asyncio.wait_for(future, timeout=300.0)
        except TimeoutError:
            manager.pending_api_requests.pop(request_id, None)
            logger.error(f"请求 {request_id} 超时 (300s)。")
            raise HTTPException(status_code=504, detail="Request to Userscript timed out.")
        except HTTPException as e:  # Userscript 返回的错误被包装成了 HTTPException
            logger.error(f"请求 {request_id} 失败 (来自 Userscript): {e.detail}")
            # manager.pending_api_requests.pop(request_id, None) # future.set_exception 时已移除
            raise e  # 原样抛出
        finally:
            # 确保即使在上面没有捕获到的异常情况下也移除
            manager.pending_api_requests.pop(request_id, None)

        if not isinstance(result_data, dict):
            logger.error(
                f"从 Userscript 收到的 result_data 不是字典类型: {type(result_data)}, 内容: {str(result_data)[:100]}"
            )
            reply_text = f"[错误: Userscript 返回了非预期的数据格式: {type(result_data)}]"
            # gemini_metadata_from_response = None
        else:
            reply_text = result_data.get("text", "Userscript 未返回有效文本。")
            # gemini_metadata_from_response = result_data.get("metadata")

        # 简单的 token 计算 (非精确)
        prompt_tokens = len(prompt_text.encode("utf-8")) // 3  # 估算
        completion_tokens = len(reply_text.encode("utf-8")) // 3  # 估算
        for f_info in files_for_userscript:
            # base64 字符串长度大约是原始数据的 4/3，每个字符1字节
            # 假设每 750 个 base64 字符约等于 1 token (非常粗略的估计)
            prompt_tokens += len(f_info.get("base64", "")) // 750

        response_dict: dict[str, Any] = {
            "id": f"chatcmpl-{uuid.uuid4()}",
            "object": "chat.completion",
            "created": int(time.time()),
            "model": request.model,
            "choices": [
                {
                    "index": 0,
                    "message": {"role": "assistant", "content": reply_text},
                    "finish_reason": "stop",  # 假设非流式总是 stop
                }
            ],
            "usage": {
                "prompt_tokens": prompt_tokens,
                "completion_tokens": completion_tokens,
                "total_tokens": prompt_tokens + completion_tokens,
            },
            # "system_fingerprint": None, # OpenAI 新增字段
            # "gemini_chat_metadata": gemini_metadata_from_response, # 可选：返回元数据给客户端
        }
        return response_dict
