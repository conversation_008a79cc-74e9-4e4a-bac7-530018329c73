/**
 * AI Studio 2 API - TypeScript Type Definitions
 * 
 * 定义了项目中使用的所有类型接口
 */

// --- 基础类型 ---

export interface ModelInfo {
  id: string;
  name: string;
  displayName: string;
  description?: string;
  inputTokenLimit?: number;
  outputTokenLimit?: number;
  supportedGenerationMethods?: string[];
}

export interface GenerationConfig {
  temperature?: number;
  topP?: number;
  topK?: number;
  maxOutputTokens?: number;
  stopSequences?: string[];
  candidateCount?: number;
}

export interface SafetySetting {
  category: string;
  threshold: string;
}

// --- API 请求/响应类型 ---

export interface GenerateContentRequest {
  model: string;
  contents: Content[];
  generationConfig?: GenerationConfig;
  safetySettings?: SafetySetting[];
  tools?: Tool[];
  toolConfig?: ToolConfig;
  systemInstruction?: Content;
}

export interface Content {
  parts: Part[];
  role?: string;
}

export interface Part {
  text?: string;
  inlineData?: InlineData;
  fileData?: FileData;
  functionCall?: FunctionCall;
  functionResponse?: FunctionResponse;
}

export interface InlineData {
  mimeType: string;
  data: string;
}

export interface FileData {
  mimeType: string;
  fileUri: string;
}

export interface FunctionCall {
  name: string;
  args: Record<string, any>;
}

export interface FunctionResponse {
  name: string;
  response: Record<string, any>;
}

export interface Tool {
  functionDeclarations?: FunctionDeclaration[];
}

export interface FunctionDeclaration {
  name: string;
  description: string;
  parameters?: Schema;
}

export interface Schema {
  type: string;
  properties?: Record<string, Schema>;
  required?: string[];
  items?: Schema;
}

export interface ToolConfig {
  functionCallingConfig?: FunctionCallingConfig;
}

export interface FunctionCallingConfig {
  mode?: string;
  allowedFunctionNames?: string[];
}

export interface GenerateContentResponse {
  candidates?: Candidate[];
  promptFeedback?: PromptFeedback;
  usageMetadata?: UsageMetadata;
}

export interface Candidate {
  content?: Content;
  finishReason?: string;
  index?: number;
  safetyRatings?: SafetyRating[];
}

export interface SafetyRating {
  category: string;
  probability: string;
  blocked?: boolean;
}

export interface PromptFeedback {
  blockReason?: string;
  safetyRatings?: SafetyRating[];
}

export interface UsageMetadata {
  promptTokenCount?: number;
  candidatesTokenCount?: number;
  totalTokenCount?: number;
}

// --- WebSocket 消息类型 ---

export interface WebSocketMessage {
  type: string;
  requestId?: string;
  data?: any;
  error?: string;
  timestamp?: string;
}

export interface ApiRequestMessage extends WebSocketMessage {
  type: 'api_request';
  requestId: string;
  data: {
    model: string;
    messages: ChatMessage[];
    stream?: boolean;
    temperature?: number;
    maxTokens?: number;
    topP?: number;
    stopSequences?: string[];
  };
}

export interface ApiResponseMessage extends WebSocketMessage {
  type: 'api_response' | 'api_stream_chunk' | 'api_stream_end' | 'api_error';
  requestId: string;
  data?: {
    content?: string;
    delta?: { content: string };
    finishReason?: string;
    usage?: UsageMetadata;
  };
}

export interface UserscriptReadyMessage extends WebSocketMessage {
  type: 'userscript_ready';
  data: {
    models: ModelInfo[];
    capabilities: string[];
    version: string;
  };
}

// --- Chat 消息类型 ---

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  name?: string;
}

// --- DOM 相关类型 ---

export interface DOMSelectors {
  promptInput: string;
  submitButton: string;
  responseContainer: string;
  responseText: string;
  loadingSpinner: string;
  modelSelector: string;
  temperatureInput: string;
  topPInput: string;
  maxTokensInput: string;
  stopSequenceInput: string;
}

export interface UIElements {
  promptInput?: HTMLTextAreaElement | HTMLElement;
  submitButton?: HTMLButtonElement;
  responseContainer?: HTMLElement;
  modelSelector?: HTMLSelectElement;
  temperatureInput?: HTMLInputElement;
  topPInput?: HTMLInputElement;
  maxTokensInput?: HTMLInputElement;
  stopSequenceInput?: HTMLInputElement;
}

// --- 配置类型 ---

export interface UserscriptConfig {
  serverUrl: string;
  wsUrl: string;
  apiKey?: string;
  debug: boolean;
  autoConnect: boolean;
  retryAttempts: number;
  retryDelay: number;
}

export interface AuthCredentials {
  cookies: Record<string, string>;
  sessionToken?: string;
  csrfToken?: string;
}

// --- 事件类型 ---

export interface CustomEventMap {
  'aistudio:ready': CustomEvent<{ models: ModelInfo[] }>;
  'aistudio:request': CustomEvent<{ request: GenerateContentRequest }>;
  'aistudio:response': CustomEvent<{ response: GenerateContentResponse }>;
  'aistudio:error': CustomEvent<{ error: Error }>;
}

// --- 错误类型 ---

export class AiStudioError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message);
    this.name = 'AiStudioError';
  }
}

export class AuthenticationError extends AiStudioError {
  constructor(message: string, details?: any) {
    super(message, 'AUTH_ERROR', details);
    this.name = 'AuthenticationError';
  }
}

export class NetworkError extends AiStudioError {
  constructor(message: string, details?: any) {
    super(message, 'NETWORK_ERROR', details);
    this.name = 'NetworkError';
  }
}

export class DOMError extends AiStudioError {
  constructor(message: string, details?: any) {
    super(message, 'DOM_ERROR', details);
    this.name = 'DOMError';
  }
}

// --- 工具类型 ---

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface Logger {
  debug(message: string, ...args: any[]): void;
  info(message: string, ...args: any[]): void;
  warn(message: string, ...args: any[]): void;
  error(message: string, ...args: any[]): void;
}

export type EventCallback<T = any> = (data: T) => void | Promise<void>;

export interface EventEmitter {
  on<K extends keyof CustomEventMap>(event: K, callback: EventCallback<CustomEventMap[K]['detail']>): void;
  off<K extends keyof CustomEventMap>(event: K, callback: EventCallback<CustomEventMap[K]['detail']>): void;
  emit<K extends keyof CustomEventMap>(event: K, data: CustomEventMap[K]['detail']): void;
}

// --- Tampermonkey 全局类型扩展 ---

declare global {
  interface Window {
    GM_xmlhttpRequest: (details: any) => void;
    GM_cookie: {
      list: (details: any, callback: (cookies: any[], error?: any) => void) => void;
    };
    GM_setValue: (key: string, value: any) => void;
    GM_getValue: (key: string, defaultValue?: any) => any;
    GM_registerMenuCommand: (caption: string, commandFunc: () => void, accessKey?: string) => void;
    GM_setClipboard: (data: string, type?: string) => void;
  }
}
