#!/usr/bin/env python3
"""
完整流程测试脚本

测试 AI Studio 2 API 的完整功能流程：
1. 服务器接收 AI 应用的聊天完成请求
2. 服务器发送指令到用户脚本
3. 用户脚本执行页面操作
4. 用户脚本返回响应数据
5. 服务器返回给 AI 应用
"""

import asyncio
import json
import logging
import aiohttp
import websockets
from datetime import datetime

# 配置
SERVER_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws/aistudio"

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_complete_flow():
    """测试完整的 AI Studio API 流程"""
    
    print("🚀 开始测试 AI Studio 2 API 完整流程...")
    
    # 1. 测试服务器健康检查
    print("\n1️⃣ 测试服务器健康检查...")
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{SERVER_URL}/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 服务器健康检查通过: {data}")
                else:
                    print(f"❌ 服务器健康检查失败: {response.status}")
                    return
        except Exception as e:
            print(f"❌ 无法连接到服务器: {e}")
            return
    
    # 2. 模拟用户脚本连接
    print("\n2️⃣ 模拟用户脚本连接...")
    try:
        async with websockets.connect(WS_URL) as websocket:
            print("✅ WebSocket 连接成功")
            
            # 发送用户脚本就绪消息
            ready_message = {
                "type": "userscript_ready",
                "data": {
                    "models": [
                        {
                            "id": "models/gemini-2.0-flash",
                            "name": "gemini-2.0-flash",
                            "displayName": "Gemini 2.0 Flash",
                            "description": "Fast and efficient model"
                        },
                        {
                            "id": "models/gemini-1.5-pro",
                            "name": "gemini-1.5-pro", 
                            "displayName": "Gemini 1.5 Pro",
                            "description": "Advanced reasoning model"
                        }
                    ],
                    "capabilities": [
                        "text_generation",
                        "model_switching",
                        "parameter_control",
                        "stream_response",
                        "chat_clearing",
                        "page_automation"
                    ],
                    "version": "1.0.0"
                },
                "timestamp": datetime.now().isoformat()
            }
            
            await websocket.send(json.dumps(ready_message))
            print("✅ 已发送用户脚本就绪消息")
            
            # 3. 测试聊天完成 API
            print("\n3️⃣ 测试聊天完成 API...")
            
            # 创建聊天完成请求
            chat_request = {
                "model": "gemini-2.0-flash",
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一个有用的AI助手。"
                    },
                    {
                        "role": "user", 
                        "content": "请简单介绍一下人工智能。"
                    }
                ],
                "temperature": 0.7,
                "max_tokens": 1000,
                "stream": False
            }
            
            # 发送请求到服务器
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.post(
                        f"{SERVER_URL}/v1/aistudio/chat/completions",
                        json=chat_request,
                        headers={"Content-Type": "application/json"}
                    ) as response:
                        print(f"📤 已发送聊天完成请求，状态码: {response.status}")
                        
                        if response.status == 200:
                            # 4. 监听用户脚本指令
                            print("\n4️⃣ 等待服务器指令...")
                            
                            try:
                                # 等待服务器发送的指令
                                message_data = await asyncio.wait_for(
                                    websocket.recv(), 
                                    timeout=10.0
                                )
                                
                                message = json.loads(message_data)
                                print(f"📨 收到服务器指令: {message.get('type')}")
                                print(f"📋 指令详情: {json.dumps(message, indent=2, ensure_ascii=False)}")
                                
                                if message.get("type") == "execute_chat_request":
                                    # 5. 模拟用户脚本执行页面操作
                                    print("\n5️⃣ 模拟执行页面操作...")
                                    request_id = message.get("requestId")
                                    
                                    # 模拟执行操作序列
                                    actions = message.get("data", {}).get("actions", [])
                                    for i, action in enumerate(actions):
                                        print(f"   执行操作 {i+1}: {action.get('type')}")
                                        await asyncio.sleep(0.5)  # 模拟操作时间
                                    
                                    # 6. 发送模拟响应
                                    print("\n6️⃣ 发送模拟响应...")
                                    response_message = {
                                        "type": "api_response",
                                        "requestId": request_id,
                                        "data": {
                                            "content": "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。AI包括机器学习、深度学习、自然语言处理等多个子领域，正在改变我们的生活和工作方式。",
                                            "finishReason": "stop"
                                        },
                                        "timestamp": datetime.now().isoformat()
                                    }
                                    
                                    await websocket.send(json.dumps(response_message))
                                    print("✅ 已发送模拟响应")
                                    
                                    # 7. 检查最终响应
                                    print("\n7️⃣ 检查 API 响应...")
                                    response_data = await response.json()
                                    print(f"✅ 收到最终响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
                                    
                                else:
                                    print(f"❌ 收到意外的消息类型: {message.get('type')}")
                                    
                            except asyncio.TimeoutError:
                                print("❌ 等待服务器指令超时")
                                
                        else:
                            error_data = await response.text()
                            print(f"❌ 聊天完成请求失败: {response.status} - {error_data}")
                            
                except Exception as e:
                    print(f"❌ 发送聊天完成请求失败: {e}")
                    
    except Exception as e:
        print(f"❌ WebSocket 连接失败: {e}")
    
    print("\n🎯 测试完成！")

async def test_model_list():
    """测试模型列表 API"""
    print("\n📋 测试模型列表 API...")
    
    async with aiohttp.ClientSession() as session:
        try:
            async with session.get(f"{SERVER_URL}/v1/models") as response:
                if response.status == 200:
                    data = await response.json()
                    models = data.get("data", [])
                    print(f"✅ 获取到 {len(models)} 个模型")
                    for model in models[:3]:  # 显示前3个模型
                        print(f"   - {model.get('id')}: {model.get('display_name', 'N/A')}")
                else:
                    print(f"❌ 模型列表请求失败: {response.status}")
        except Exception as e:
            print(f"❌ 模型列表请求异常: {e}")

if __name__ == "__main__":
    print("=" * 60)
    print("AI Studio 2 API 完整流程测试")
    print("=" * 60)
    
    # 运行测试
    asyncio.run(test_model_list())
    asyncio.run(test_complete_flow())
