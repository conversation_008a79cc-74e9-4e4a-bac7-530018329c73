# 老哥带你玩转 Gemini Web 代理

## 项目简介

兄弟们，整了个活儿！这个项目就是个小工具，能让你用第三方 AI 客户端或者自己写点脚本，通过你的浏览器会话去调 Google Gemini Web 版。简单说，就是搭个桥，让你绕过一些限制，用起来更顺手。

## 这玩意儿是干啥的？

这项目分两部分：

1. **服务器端 (Python FastAPI)**
    * 用 Python 写的，基于 FastAPI 框架。
    * 跑在你本地，就像个中转站。
    * 它能接收符合 OpenAI API 格式的请求（比如聊天和模型列表）。
    * 通过 WebSocket 跟浏览器里的 UserScript 连着。
    * 把客户端的请求发给 UserScript 去执行，再把 UserScript 拿到的结果按 OpenAI API 格式返回给客户端。
    * 支持流式和非流式响应，还有个简单的 API Key 验证。

2. **浏览器脚本 (Userscript)**
    * 这是个 Tampermonkey 脚本，得装浏览器里（比如 Chrome 装 Tampermonkey 插件）。
    * 它会注入到 `gemini.google.com` 或者 `aistudio.google.com` 页面里。
    * 这脚本是干脏活累活的，它负责在浏览器里模拟你的操作，跟 Gemini 的网页界面打交道。
    * 它会跟本地的 Python 后端建立 WebSocket 连接。
    * 收到后端的指令后，它就去页面上找输入框、输文字、点发送按钮，就像你在操作一样。
    * 它还会盯着页面看，一旦 Gemini 开始回复，它就抓取回复内容（包括那种一个字一个字蹦出来的流式回复），然后通过 WebSocket 发回给后端。
    * 另外，它还能帮你抓取一些重要的信息，比如你的 Cookie 和那个叫 SNlM0e 的令牌，还有页面上能用的模型信息，都发给后端。
    * 甚至还能帮你把图片文件传到 Google 的上传服务去。

## 核心思想

这套组合拳，就是想利用你已经登录的浏览器会话，通过模拟人工操作的方式去访问 Google Gemini Web。这样一来，你就可以用习惯的第三方客户端来调 Gemini，而且相比直接用一些不稳定的 API 或者自己搞浏览器自动化，这种方式可能更安全点，不容易被封号。

## 怎么跑起来？

### 1. 跑服务器端

* 确保你装了 Python 环境。
* 进入项目的 `server` 目录。
* 安装依赖：

    ```bash
    pip install -r requirements.txt
    ```

    （如果 `requirements.txt` 文件不存在，可能需要手动安装 FastAPI 和 websockets 等库，或者等我后续更新说明）
* 运行服务器：

    ```bash
    python -m server
    ```

    默认会在本地的 8000 端口启动。

### 2. 安装和使用浏览器脚本

* 首先，你得在浏览器里装个 Tampermonkey 插件。
* 然后，打开 `userscript` 目录下的 [`gemini.user.js`](userscript/gemini.user.js) 文件。
* 把里面的代码复制粘贴到 Tampermonkey 的新建脚本里，然后保存启用。
* 确保你的浏览器能访问 `gemini.google.com` 或 `aistudio.google.com`，并且你已经登录了 Google 账号。
* 同时，确保本地的 Python 服务器正在运行。
* 脚本会自动尝试连接本地服务器。连接成功后，你就可以通过本地服务器的 OpenAI API 接口来调用 Gemini 了。

## 注意事项&&已知BUG

* 这玩意儿是模拟人工操作，所以速度可能不如直接调用官方 API 快。
* Google 可能会更新网页结构，导致脚本失效，到时候可能需要更新脚本。
* 有概率出现截断/只出来一个'c'的情况，应该是`sendPromptToGemini`没有写好。

## 致谢

本项目参考了以下开源项目，在此表示感谢：

* [zhiyu1998/Gemi2Api-Server](https://github.com/zhiyu1998/Gemi2Api-Server)
* [HanaokaYuzu/Gemini-API](https://github.com/HanaokaYuzu/Gemini-API)

---
