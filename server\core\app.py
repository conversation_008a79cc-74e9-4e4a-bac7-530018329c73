import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(_app: FastAPI):
    """应用生命周期管理"""
    logger.info("🚀 启动 AI Studio 2 API 服务器...")
    logger.info("使用 XHR 拦截方式，无需启动 HTTPS 代理服务器")

    yield

    logger.info("🔄 清理应用资源...")
    logger.info("✅ 应用资源清理完成")


# --- FastAPI 应用实例 ---
app = FastAPI(
    title="Gemini Userscript Bridge API Server",
    description="通过 Userscript 与 Gemini Web 交互的代理服务器",
    version="1.0.0",
    lifespan=lifespan,
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境建议限制来源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
