import json
from pathlib import Path
from typing import Any


def extract_message(data: list[Any]) -> tuple[str, bool]:
    message_list, *is_done = data[0][0]
    assert message_list[1] == "model", ValueError("message_list[1] is not 'model'")
    return message_list[0][0][1], is_done == [1]


class PartialParser:
    """
    [[
    [[[[[[None,"THINK",None,None,None,None,None,None,None,None,None,None,1]],"model"]]],None,[477,None,551,None,[[1,219],[2,258]],None,None,None,None,74]],
    # .......... lots of message
    [[[[[[None,"MESSAGE"]],"model"]]],None,[477,4,1569,None,[[1,219],[2,258]],None,None,None,None,1088]],
    # .......... lots of message
    # 此处的",1]]"暗示是最后一条消息
    [[[[[[None,"MESSAGE"]],"model"],1]],None,[477,599,2164,None,[[1,219],[2,258]],None,None,None,None,1088]],
    # last message
    [None,None,None,["1747981972492120",5494348,3070878298]]
    ]]
    """

    def __init__(self):
        self.fist_loaded = False
        self.reach_end = False
        self.buffer = ""

    def feed(self, chunk: str) -> list[tuple[str, bool]]:
        increment = chunk[len(self.buffer) :]
        self.buffer = chunk
        if not self.fist_loaded:
            # 移除多余的 "[[", 并标记为已加载
            increment = increment[2:]
            self.fist_loaded = True
        else:
            # 会多一个逗号
            increment = increment[1:]
        if increment.endswith("]]]]"):
            # 判断是否是最后一块数据
            # 移除末尾的 "]]]]", 并标记为结束
            increment = increment[:-2]
            self.reach_end = True
        try:
            # 包裹一层[]
            # 尝试解析 JSON
            # 只解析新增的部分
            increment_data = json.loads("[" + increment + "]")
            if self.reach_end:
                increment_data = increment_data[:-1]
            return [extract_message(data) for data in increment_data]
        except json.JSONDecodeError:
            raise ValueError("Invalid JSON format")


if __name__ == "__main__":
    data = Path("saved.txt").read_text(encoding="utf-8").splitlines()
    parser = PartialParser()
    for text in data:
        for chunks in parser.feed(text):
            message, is_done = chunks
            print(message, end="")
