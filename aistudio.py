"""
旧版 AI Studio 测试服务器
现在重定向到新的服务器端点
"""

import requests
from flask import Flask, jsonify, request

app = Flask(__name__)

# 新服务器的端点
NEW_SERVER_URL = "http://localhost:8000/aistudio/save_message"


@app.route("/save_message", methods=["POST"])
def save_message():
    """
    兼容性端点，将请求转发到新的服务器
    """
    if request.method != "POST":
        return jsonify({"error": "Method Not Allowed"}), 405

    try:
        # 获取原始数据
        if request.is_json:
            message_data = request.get_json()
        else:
            message_data = request.get_data(as_text=True)

        if message_data is None:
            return jsonify({"error": "Invalid JSON or missing Content-Type header"}), 400

        # 转发到新服务器
        try:
            response = requests.post(
                NEW_SERVER_URL, json=message_data, headers={"Content-Type": "application/json"}, timeout=10
            )

            if response.status_code == 200:
                print("[兼容性服务器] 成功转发数据到新服务器")
                return jsonify({"message": "Message forwarded and saved successfully!"})
            else:
                print(f"[兼容性服务器] 转发失败，状态码: {response.status_code}")
                # 如果转发失败，仍然保存到本地文件
                with open("saved.txt", "a", encoding="utf-8") as f:
                    f.write(str(message_data) + "\n")
                return jsonify({"message": "Message saved locally (forward failed)!"})

        except requests.RequestException as e:
            print(f"[兼容性服务器] 转发请求失败: {e}")
            # 如果转发失败，保存到本地文件
            with open("saved.txt", "a", encoding="utf-8") as f:
                f.write(str(message_data) + "\n")
            return jsonify({"message": "Message saved locally (server unavailable)!"})

    except Exception as e:
        return jsonify({"error": f"Error processing request: {e}"}), 400


if __name__ == "__main__":
    print("启动兼容性 AI Studio 服务器 (端口 8001)")
    print("此服务器将请求转发到新的服务器 (端口 8000)")
    print("建议直接使用新服务器: python -m server")
    app.run(host="localhost", port=8001)
