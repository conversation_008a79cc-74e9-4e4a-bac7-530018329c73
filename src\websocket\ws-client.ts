/**
 * AI Studio 2 API - WebSocket Client
 *
 * 处理与服务器的 WebSocket 通信
 */

import type {
  WebSocketMessage,
  ApiRequestMessage,
  ApiResponseMessage,
  UserscriptReadyMessage,
  ModelInfo
} from '../types';
import {
  MESSAGE_TYPES,
  TIMEOUTS,
  VERSION_INFO
} from '../config/constants';
import { logger, delay } from '../utils/helpers';
import { NetworkError } from '../types';

export interface WebSocketEvents {
  connected: () => void;
  disconnected: () => void;
  apiRequest: (message: ApiRequestMessage) => void;
  chatRequest: (message: any) => void;
  error: (error: Error) => void;
  reconnecting: (attempt: number) => void;
}

export class WebSocketClient {
  private ws: WebSocket | null = null;
  private url: string;
  private isConnecting = false;
  private isReconnecting = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private eventHandlers: Partial<WebSocketEvents> = {};
  private heartbeatInterval: number | null = null;
  private connectionTimeout: number | null = null;

  constructor(url: string) {
    this.url = url;
    this.bindMethods();
  }

  /**
   * 绑定方法到实例
   */
  private bindMethods(): void {
    this.connect = this.connect.bind(this);
    this.disconnect = this.disconnect.bind(this);
    this.send = this.send.bind(this);
    this.handleMessage = this.handleMessage.bind(this);
    this.handleClose = this.handleClose.bind(this);
    this.handleError = this.handleError.bind(this);
  }

  /**
   * 连接到服务器
   */
  public async connect(): Promise<void> {
    if (this.isConnected()) {
      logger.info('WebSocket 已连接');
      return;
    }

    if (this.isConnecting) {
      logger.info('WebSocket 连接正在进行中...');
      return;
    }

    this.isConnecting = true;

    try {
      logger.info(`连接到 WebSocket 服务器: ${this.url}`);

      await this.performConnect();

      this.isConnecting = false;
      this.reconnectAttempts = 0;

      logger.info('WebSocket 连接成功');
      this.emit('connected');

      // 发送就绪消息
      await this.sendReadyMessage();

    } catch (error) {
      this.isConnecting = false;
      const wsError = new NetworkError(
        `WebSocket 连接失败: ${error instanceof Error ? error.message : String(error)}`,
        { url: this.url, originalError: error }
      );

      logger.error('WebSocket 连接失败:', wsError);
      this.emit('error', wsError);

      throw wsError;
    }
  }

  /**
   * 执行连接
   */
  private async performConnect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url);

        // 设置连接超时
        this.connectionTimeout = window.setTimeout(() => {
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close();
            reject(new Error('连接超时'));
          }
        }, TIMEOUTS.WS_CONNECT_TIMEOUT);

        this.ws.onopen = () => {
          if (this.connectionTimeout) {
            clearTimeout(this.connectionTimeout);
            this.connectionTimeout = null;
          }

          this.setupHeartbeat();
          resolve();
        };

        this.ws.onmessage = this.handleMessage;
        this.ws.onclose = this.handleClose;
        this.ws.onerror = this.handleError;

      } catch (error) {
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout);
          this.connectionTimeout = null;
        }
        reject(error);
      }
    });
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    logger.info('断开 WebSocket 连接');

    this.isReconnecting = false;
    this.clearHeartbeat();

    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout);
      this.connectionTimeout = null;
    }

    if (this.ws) {
      this.ws.onclose = null; // 防止触发重连
      this.ws.close();
      this.ws = null;
    }

    this.emit('disconnected');
  }

  /**
   * 发送消息
   */
  public async send(message: WebSocketMessage): Promise<void> {
    if (!this.isConnected()) {
      throw new NetworkError('WebSocket 未连接');
    }

    try {
      const messageStr = JSON.stringify({
        ...message,
        timestamp: new Date().toISOString(),
      });

      this.ws!.send(messageStr);
      logger.debug('发送 WebSocket 消息:', message.type);
    } catch (error) {
      const wsError = new NetworkError(
        `发送 WebSocket 消息失败: ${error instanceof Error ? error.message : String(error)}`,
        { message, originalError: error }
      );

      logger.error('发送消息失败:', wsError);
      throw wsError;
    }
  }

  /**
   * 发送就绪消息
   */
  public async sendReadyMessage(models: ModelInfo[] = []): Promise<void> {
    const readyMessage: UserscriptReadyMessage = {
      type: MESSAGE_TYPES.USERSCRIPT_READY,
      data: {
        models,
        capabilities: [
          'text_generation',
          'model_switching',
          'parameter_control',
          'stream_response',
        ],
        version: VERSION_INFO.VERSION,
      },
    };

    await this.send(readyMessage);
    logger.info('已发送用户脚本就绪消息');
  }

  /**
   * 发送 API 响应
   */
  public async sendApiResponse(
    requestId: string,
    content: string,
    finishReason: string = 'stop'
  ): Promise<void> {
    const response: ApiResponseMessage = {
      type: MESSAGE_TYPES.API_RESPONSE,
      requestId,
      data: {
        content,
        finishReason,
      },
    };

    await this.send(response);
  }

  /**
   * 发送流式响应块
   */
  public async sendStreamChunk(
    requestId: string,
    delta: string
  ): Promise<void> {
    const chunk: ApiResponseMessage = {
      type: MESSAGE_TYPES.API_STREAM_CHUNK,
      requestId,
      data: {
        delta: { content: delta },
      },
    };

    await this.send(chunk);
  }

  /**
   * 发送流式响应结束
   */
  public async sendStreamEnd(
    requestId: string,
    finishReason: string = 'stop'
  ): Promise<void> {
    const end: ApiResponseMessage = {
      type: MESSAGE_TYPES.API_STREAM_END,
      requestId,
      data: {
        finishReason,
      },
    };

    await this.send(end);
  }

  /**
   * 发送错误消息
   */
  public async sendError(
    requestId: string,
    error: string | Error
  ): Promise<void> {
    const errorMessage: ApiResponseMessage = {
      type: MESSAGE_TYPES.API_ERROR,
      requestId,
      error: error instanceof Error ? error.message : error,
    };

    await this.send(errorMessage);
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      logger.debug('收到 WebSocket 消息:', message.type);

      switch (message.type) {
        case MESSAGE_TYPES.API_REQUEST:
          this.emit('apiRequest', message as ApiRequestMessage);
          break;

        case 'execute_chat_request':
          this.emit('chatRequest', message);
          break;

        case MESSAGE_TYPES.CONNECTION_STATUS:
          logger.info('服务器连接状态:', message.data);
          break;

        case MESSAGE_TYPES.SERVER_ERROR:
          logger.error('服务器错误:', message.error);
          this.emit('error', new Error(message.error || '服务器错误'));
          break;

        default:
          logger.warn('未知消息类型:', message.type);
      }
    } catch (error) {
      logger.error('解析 WebSocket 消息失败:', error);
      this.emit('error', new Error('消息解析失败'));
    }
  }

  /**
   * 处理连接关闭
   */
  private handleClose(event: CloseEvent): void {
    logger.warn(`WebSocket 连接关闭: ${event.code} - ${event.reason}`);

    this.clearHeartbeat();
    this.ws = null;

    if (!this.isReconnecting && event.code !== 1000) {
      // 非正常关闭，尝试重连
      this.attemptReconnect();
    } else {
      this.emit('disconnected');
    }
  }

  /**
   * 处理连接错误
   */
  private handleError(event: Event): void {
    logger.error('WebSocket 错误:', event);

    const error = new NetworkError('WebSocket 连接错误', { event });
    this.emit('error', error);
  }

  /**
   * 尝试重连
   */
  private async attemptReconnect(): Promise<void> {
    if (this.isReconnecting || this.reconnectAttempts >= this.maxReconnectAttempts) {
      return;
    }

    this.isReconnecting = true;
    this.reconnectAttempts++;

    logger.info(`尝试重连 WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
    this.emit('reconnecting', this.reconnectAttempts);

    try {
      await delay(TIMEOUTS.WS_RECONNECT_DELAY * this.reconnectAttempts);
      await this.connect();
      this.isReconnecting = false;
    } catch (error) {
      this.isReconnecting = false;

      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        logger.error('WebSocket 重连失败，已达到最大重试次数');
        this.emit('error', new NetworkError('重连失败'));
      } else {
        // 继续尝试重连
        setTimeout(() => this.attemptReconnect(), 1000);
      }
    }
  }

  /**
   * 设置心跳
   */
  private setupHeartbeat(): void {
    this.heartbeatInterval = window.setInterval(() => {
      if (this.isConnected()) {
        this.send({ type: 'ping' }).catch(error => {
          logger.warn('心跳发送失败:', error);
        });
      }
    }, 30000); // 30秒心跳
  }

  /**
   * 清除心跳
   */
  private clearHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * 检查是否已连接
   */
  public isConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  /**
   * 获取连接状态
   */
  public getConnectionState(): string {
    if (!this.ws) return 'CLOSED';

    switch (this.ws.readyState) {
      case WebSocket.CONNECTING: return 'CONNECTING';
      case WebSocket.OPEN: return 'OPEN';
      case WebSocket.CLOSING: return 'CLOSING';
      case WebSocket.CLOSED: return 'CLOSED';
      default: return 'UNKNOWN';
    }
  }

  /**
   * 注册事件处理器
   */
  public on<K extends keyof WebSocketEvents>(
    event: K,
    handler: WebSocketEvents[K]
  ): void {
    this.eventHandlers[event] = handler;
  }

  /**
   * 移除事件处理器
   */
  public off<K extends keyof WebSocketEvents>(event: K): void {
    delete this.eventHandlers[event];
  }

  /**
   * 触发事件
   */
  private emit<K extends keyof WebSocketEvents>(
    event: K,
    ...args: Parameters<NonNullable<WebSocketEvents[K]>>
  ): void {
    const handler = this.eventHandlers[event];
    if (handler) {
      try {
        (handler as any)(...args);
      } catch (error) {
        logger.error(`WebSocket 事件处理器执行失败 (${event}):`, error);
      }
    }
  }
}
