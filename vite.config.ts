import { defineConfig } from 'vite';
import monkey from 'vite-plugin-monkey';


// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    monkey({
      entry: "src/main.ts",
      userscript: {
        name: {
          en: "AI-studio web to api",
          zh: "AI-studio 网页转 API",
        },
        namespace: "https://github.com/lll9p/aistudio2api",
        version: "0.0.1",
        icon: "https://aistudio.google.com/favicon.ico",
        description: {
          "": "A userscript to convert AI-studio web pages to API.",
          zh: "一个将 AI-studio 网页转换为 API 的用户脚本。",
          en: "A userscript to convert AI-studio web pages to API.",
        },
        author: "lll9p",
        match: ["https://aistudio.google.com/*"],
        grant: [
          "GM_xmlhttpRequest",
          "GM_cookie",
          "GM_setValue",
          "GM_getValue",
          "GM_registerMenuCommand",
          "GM_setClipboard"
        ],
        connect: [
          "localhost",
          "aistudio.google.com",
          "alkalimakersuite-pa.clients6.google.com"
        ],
        "run-at": "document-start",
        noframes: true
      },
    }),
  ],
});