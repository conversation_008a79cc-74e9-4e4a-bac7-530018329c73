# See the configuration reference at
# https://github.com/crate-ci/typos/blob/master/docs/reference.md

# Corrections take the form of a key/value pair. The key is the incorrect word
# and the value is the correct word. If the key and value are the same, the
# word is treated as always correct. If the value is an empty string, the word
# is treated as always incorrect.

# Match Identifier - Case Sensitive
[default.extend-identifiers]
Nwo = "Nwo"
nwo = "nwo"

# Match Inside a Word - Case Insensitive
[default.extend-words]
Nwo = "Nwo"
nwo = "nwo"

[files]
# Include .github, .cargo, etc.
ignore-hidden = false
# /.git isn't in .gitignore, because git never tracks it.
# Typos doesn't know that, though.
extend-exclude = ["/.git"]
